import React from 'react';
import { useTranslation } from 'react-i18next';
import { X, MessageCircle, Send, Phone } from 'lucide-react';
import './CustomerSupportModal.css';

const CustomerSupportModal = ({ isOpen, onClose }) => {
  const { t } = useTranslation();

  if (!isOpen) return null;

  const handleWhatsApp = () => {
    window.open('https://wa.me/905555555555', '_blank');
  };

  const handleTelegram = () => {
    window.open('https://t.me/novayield_support', '_blank');
  };

  const handleLiveChat = () => {
    // Site içi canlı sohbet sistemi burada açılacak
    console.log('Live chat opened');
    onClose();
  };

  return (
    <div className="support-modal-backdrop" onClick={onClose}>
      <div className="support-modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="support-modal-header">
          <h2 className="support-modal-title">{t('support.title')}</h2>
          <button className="support-modal-close" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="support-modal-body">
          <p className="support-modal-description">
            {t('support.description')}
          </p>

          <div className="support-options">
            <button className="support-option whatsapp" onClick={handleWhatsApp}>
              <div className="support-icon whatsapp-icon">
                <MessageCircle size={24} />
              </div>
              <div className="support-info">
                <h3 className="support-title">{t('support.whatsapp')}</h3>
                <p className="support-description">{t('support.whatsapp_desc')}</p>
              </div>
              <div className="support-arrow">→</div>
            </button>

            <button className="support-option telegram" onClick={handleTelegram}>
              <div className="support-icon telegram-icon">
                <Send size={24} />
              </div>
              <div className="support-info">
                <h3 className="support-title">{t('support.telegram')}</h3>
                <p className="support-description">{t('support.telegram_desc')}</p>
              </div>
              <div className="support-arrow">→</div>
            </button>

            <button className="support-option live-chat" onClick={handleLiveChat}>
              <div className="support-icon live-chat-icon">
                <Phone size={24} />
              </div>
              <div className="support-info">
                <h3 className="support-title">{t('support.live_chat')}</h3>
                <p className="support-description">{t('support.live_chat_desc')}</p>
              </div>
              <div className="support-arrow">→</div>
            </button>
          </div>
        </div>

        <div className="support-modal-footer">
          <div className="support-footer-badges">
            <span className="support-badge ssl">{t('support.ssl_secure')}</span>
            <span className="support-badge usdt">{t('support.usdt_details')}</span>
            <span className="support-badge support-24-7">{t('support.support_24_7')}</span>
          </div>
          <p className="support-footer-text">{t('support.footer_text')}</p>
        </div>
      </div>
    </div>
  );
};

export default CustomerSupportModal;
