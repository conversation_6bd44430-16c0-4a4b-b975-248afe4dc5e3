import React from 'react';
import './FinancialIcons.css';

// Finansal SVG ikonları
export const CryptoChart = ({ className = "" }) => (
  <svg className={`financial-icon ${className}`} viewBox="0 0 200 120" fill="none">
    <defs>
      <linearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="var(--accent-primary)" />
        <stop offset="50%" stopColor="var(--accent-cyan)" />
        <stop offset="100%" stopColor="var(--accent-pink)" />
      </linearGradient>
      <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stopColor="var(--accent-primary)" stopOpacity="0.3" />
        <stop offset="100%" stopColor="var(--accent-primary)" stopOpacity="0.05" />
      </linearGradient>
    </defs>
    
    {/* Grid lines */}
    <g stroke="var(--border-color)" strokeWidth="0.5" opacity="0.3">
      <line x1="20" y1="20" x2="180" y2="20" />
      <line x1="20" y1="40" x2="180" y2="40" />
      <line x1="20" y1="60" x2="180" y2="60" />
      <line x1="20" y1="80" x2="180" y2="80" />
      <line x1="20" y1="100" x2="180" y2="100" />
    </g>
    
    {/* Chart area */}
    <path
      d="M20 100 L40 85 L60 70 L80 75 L100 55 L120 45 L140 35 L160 25 L180 20 L180 100 Z"
      fill="url(#areaGradient)"
    />
    
    {/* Chart line */}
    <path
      d="M20 100 L40 85 L60 70 L80 75 L100 55 L120 45 L140 35 L160 25 L180 20"
      stroke="url(#chartGradient)"
      strokeWidth="3"
      fill="none"
      className="chart-line"
    />
    
    {/* Data points */}
    <g fill="var(--accent-primary)">
      <circle cx="40" cy="85" r="3" className="data-point" />
      <circle cx="80" cy="75" r="3" className="data-point" />
      <circle cx="120" cy="45" r="3" className="data-point" />
      <circle cx="160" cy="25" r="3" className="data-point" />
      <circle cx="180" cy="20" r="4" fill="var(--accent-gold)" className="data-point-highlight" />
    </g>
  </svg>
);

export const DollarStack = ({ className = "" }) => (
  <svg className={`financial-icon ${className}`} viewBox="0 0 100 100" fill="none">
    <defs>
      <linearGradient id="dollarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="var(--accent-gold)" />
        <stop offset="100%" stopColor="var(--accent-orange)" />
      </linearGradient>
    </defs>
    
    {/* Stack of coins */}
    <ellipse cx="50" cy="85" rx="25" ry="8" fill="url(#dollarGradient)" opacity="0.6" />
    <ellipse cx="50" cy="75" rx="25" ry="8" fill="url(#dollarGradient)" opacity="0.7" />
    <ellipse cx="50" cy="65" rx="25" ry="8" fill="url(#dollarGradient)" opacity="0.8" />
    <ellipse cx="50" cy="55" rx="25" ry="8" fill="url(#dollarGradient)" />
    
    {/* Dollar sign */}
    <text x="50" y="60" textAnchor="middle" fill="white" fontSize="20" fontWeight="bold">$</text>
    
    {/* Floating coins */}
    <circle cx="20" cy="30" r="8" fill="var(--accent-gold)" opacity="0.8" className="floating-coin" />
    <text x="20" y="35" textAnchor="middle" fill="white" fontSize="8" fontWeight="bold">$</text>
    
    <circle cx="80" cy="25" r="6" fill="var(--accent-gold)" opacity="0.6" className="floating-coin" />
    <text x="80" y="29" textAnchor="middle" fill="white" fontSize="6" fontWeight="bold">$</text>
  </svg>
);

export const TrendingArrow = ({ className = "" }) => (
  <svg className={`financial-icon ${className}`} viewBox="0 0 100 100" fill="none">
    <defs>
      <linearGradient id="arrowGradient" x1="0%" y1="100%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="var(--accent-green)" />
        <stop offset="100%" stopColor="var(--accent-cyan)" />
      </linearGradient>
    </defs>
    
    {/* Arrow shaft */}
    <path
      d="M15 85 L75 25"
      stroke="url(#arrowGradient)"
      strokeWidth="6"
      strokeLinecap="round"
      className="trending-arrow"
    />
    
    {/* Arrow head */}
    <path
      d="M60 25 L75 25 L75 40"
      stroke="url(#arrowGradient)"
      strokeWidth="6"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
    
    {/* Percentage indicator */}
    <text x="85" y="20" fill="var(--accent-green)" fontSize="12" fontWeight="bold">+102%</text>
  </svg>
);

export const SecurityShield = ({ className = "" }) => (
  <svg className={`financial-icon ${className}`} viewBox="0 0 100 100" fill="none">
    <defs>
      <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="var(--accent-primary)" />
        <stop offset="100%" stopColor="var(--accent-tertiary)" />
      </linearGradient>
    </defs>

    {/* Shield shape */}
    <path
      d="M50 10 C30 10 20 20 20 30 C20 50 30 80 50 90 C70 80 80 50 80 30 C80 20 70 10 50 10 Z"
      fill="url(#shieldGradient)"
      className="security-shield"
    />

    {/* DeFi Protocol Symbol - Interconnected nodes */}
    <circle cx="40" cy="40" r="4" fill="white" opacity="0.9" />
    <circle cx="60" cy="40" r="4" fill="white" opacity="0.9" />
    <circle cx="50" cy="55" r="4" fill="white" opacity="0.9" />

    {/* Connection lines */}
    <line x1="40" y1="40" x2="60" y2="40" stroke="white" strokeWidth="2" opacity="0.7" />
    <line x1="40" y1="40" x2="50" y2="55" stroke="white" strokeWidth="2" opacity="0.7" />
    <line x1="60" y1="40" x2="50" y2="55" stroke="white" strokeWidth="2" opacity="0.7" />

    {/* Central secure node */}
    <circle cx="50" cy="45" r="3" fill="var(--accent-gold)" className="central-node" />

    {/* Security indicators */}
    <circle cx="30" cy="25" r="2" fill="white" opacity="0.6" className="security-indicator" />
    <circle cx="70" cy="25" r="2" fill="white" opacity="0.6" className="security-indicator" />
  </svg>
);

export const CryptoWallet = ({ className = "" }) => (
  <svg className={`financial-icon ${className}`} viewBox="0 0 100 100" fill="none">
    <defs>
      <linearGradient id="walletGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="var(--accent-cyan)" />
        <stop offset="100%" stopColor="var(--accent-primary)" />
      </linearGradient>
    </defs>
    
    {/* Wallet body */}
    <rect x="15" y="30" width="70" height="50" rx="8" fill="url(#walletGradient)" />
    
    {/* Wallet flap */}
    <rect x="15" y="25" width="70" height="15" rx="8" fill="var(--accent-primary)" opacity="0.8" />
    
    {/* USDT symbol */}
    <circle cx="50" cy="55" r="12" fill="white" opacity="0.9" />
    <text x="50" y="60" textAnchor="middle" fill="var(--accent-primary)" fontSize="8" fontWeight="bold">USDT</text>
    
    {/* Floating elements */}
    <circle cx="25" cy="20" r="3" fill="var(--accent-gold)" className="floating-element" />
    <circle cx="75" cy="15" r="2" fill="var(--accent-pink)" className="floating-element" />
  </svg>
);

export const GlobalNetwork = ({ className = "" }) => (
  <svg className={`financial-icon ${className}`} viewBox="0 0 100 100" fill="none">
    <defs>
      <linearGradient id="networkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="var(--accent-cyan)" />
        <stop offset="50%" stopColor="var(--accent-primary)" />
        <stop offset="100%" stopColor="var(--accent-pink)" />
      </linearGradient>
    </defs>

    {/* Globe */}
    <circle cx="50" cy="50" r="30" stroke="url(#networkGradient)" strokeWidth="2" fill="none" />

    {/* Network lines */}
    <path d="M20 50 Q50 30 80 50" stroke="var(--accent-primary)" strokeWidth="1.5" fill="none" opacity="0.7" />
    <path d="M20 50 Q50 70 80 50" stroke="var(--accent-primary)" strokeWidth="1.5" fill="none" opacity="0.7" />
    <line x1="50" y1="20" x2="50" y2="80" stroke="var(--accent-primary)" strokeWidth="1.5" opacity="0.7" />

    {/* Network nodes */}
    <circle cx="30" cy="35" r="3" fill="var(--accent-gold)" className="network-node" />
    <circle cx="70" cy="35" r="3" fill="var(--accent-green)" className="network-node" />
    <circle cx="30" cy="65" r="3" fill="var(--accent-pink)" className="network-node" />
    <circle cx="70" cy="65" r="3" fill="var(--accent-cyan)" className="network-node" />
    <circle cx="50" cy="50" r="4" fill="var(--accent-primary)" className="network-node-center" />
  </svg>
);

export const VaultLock = ({ className = "" }) => (
  <svg className={`financial-icon ${className}`} viewBox="0 0 100 100" fill="none">
    <defs>
      <linearGradient id="vaultGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="var(--accent-primary)" />
        <stop offset="50%" stopColor="var(--accent-tertiary)" />
        <stop offset="100%" stopColor="var(--accent-gold)" />
      </linearGradient>
      <linearGradient id="lockGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="var(--accent-gold)" />
        <stop offset="100%" stopColor="var(--accent-orange)" />
      </linearGradient>
    </defs>

    {/* Vault body */}
    <rect x="15" y="35" width="70" height="55" rx="8" fill="url(#vaultGradient)" className="vault-body" />

    {/* Vault door details */}
    <circle cx="50" cy="62" r="12" fill="none" stroke="white" strokeWidth="2" opacity="0.8" />
    <circle cx="50" cy="62" r="8" fill="none" stroke="white" strokeWidth="1.5" opacity="0.6" />
    <circle cx="50" cy="62" r="4" fill="white" opacity="0.9" />

    {/* Vault handle */}
    <rect x="72" y="58" width="8" height="8" rx="2" fill="var(--accent-gold)" />

    {/* Lock mechanism on top */}
    <rect x="40" y="25" width="20" height="15" rx="3" fill="url(#lockGradient)" className="lock-body" />

    {/* Lock shackle */}
    <path
      d="M42 25 C42 18 46 15 50 15 C54 15 58 18 58 25"
      stroke="url(#lockGradient)"
      strokeWidth="3"
      fill="none"
      className="lock-shackle"
    />

    {/* Security dots */}
    <circle cx="25" cy="45" r="2" fill="white" opacity="0.6" className="security-dot" />
    <circle cx="75" cy="45" r="2" fill="white" opacity="0.6" className="security-dot" />
    <circle cx="25" cy="75" r="2" fill="white" opacity="0.6" className="security-dot" />
    <circle cx="75" cy="75" r="2" fill="white" opacity="0.6" className="security-dot" />

    {/* Dollar signs inside vault */}
    <text x="35" y="55" fill="white" fontSize="8" fontWeight="bold" opacity="0.7">$</text>
    <text x="60" y="75" fill="white" fontSize="6" fontWeight="bold" opacity="0.7">$</text>
    <text x="30" y="80" fill="white" fontSize="7" fontWeight="bold" opacity="0.7">$</text>

    {/* Floating security elements */}
    <circle cx="20" cy="20" r="3" fill="var(--accent-green)" className="floating-security" />
    <circle cx="80" cy="25" r="2" fill="var(--accent-cyan)" className="floating-security" />
  </svg>
);

export const VIPCrown = ({ className = "" }) => (
  <svg className={`financial-icon ${className}`} viewBox="0 0 100 100" fill="none">
    <defs>
      <linearGradient id="crownGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFD700" />
        <stop offset="50%" stopColor="#FFA500" />
        <stop offset="100%" stopColor="#FF8C00" />
      </linearGradient>
      <linearGradient id="gemGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FF69B4" />
        <stop offset="50%" stopColor="#FF1493" />
        <stop offset="100%" stopColor="#DC143C" />
      </linearGradient>
      <filter id="glow">
        <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
        <feMerge>
          <feMergeNode in="coloredBlur"/>
          <feMergeNode in="SourceGraphic"/>
        </feMerge>
      </filter>
    </defs>

    {/* Crown base */}
    <path
      d="M15 65 L85 65 L80 75 L20 75 Z"
      fill="url(#crownGradient)"
      filter="url(#glow)"
      className="crown-base"
    />

    {/* Crown spikes */}
    <path
      d="M15 65 L25 35 L35 50 L50 25 L65 50 L75 35 L85 65"
      fill="url(#crownGradient)"
      filter="url(#glow)"
      className="crown-spikes"
    />

    {/* Center gem */}
    <polygon
      points="50,30 45,40 55,40"
      fill="url(#gemGradient)"
      className="center-gem"
    />

    {/* Side gems */}
    <circle cx="30" cy="45" r="3" fill="url(#gemGradient)" className="side-gem" />
    <circle cx="70" cy="45" r="3" fill="url(#gemGradient)" className="side-gem" />

    {/* VIP text */}
    <text x="50" y="85" textAnchor="middle" fill="#FFD700" fontSize="10" fontWeight="bold" className="vip-text">VIP</text>

    {/* Sparkles */}
    <circle cx="20" cy="20" r="1.5" fill="#FFD700" className="sparkle sparkle-1" />
    <circle cx="80" cy="25" r="1" fill="#FFD700" className="sparkle sparkle-2" />
    <circle cx="85" cy="45" r="1.5" fill="#FFD700" className="sparkle sparkle-3" />
    <circle cx="15" cy="50" r="1" fill="#FFD700" className="sparkle sparkle-4" />
  </svg>
);
