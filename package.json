{"name": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000", "lint": "echo 'Linting...' && echo 'No linter configured'", "format": "echo 'Formatting...' && echo 'No formatter configured'"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "i18next": "^24.2.0", "react-i18next": "^15.2.0", "i18next-browser-languagedetector": "^8.0.2", "lucide-react": "^0.468.0"}, "devDependencies": {"@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "vite": "^7.0.4"}}