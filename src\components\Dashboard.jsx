import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  User,
  TrendingUp,
  DollarSign,
  PieChart,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Eye,
  EyeOff,
  LogOut,
  Settings,
  Bell,
  CreditCard,
  QrCode,
  Package,
  Sun,
  Moon,
  ChevronDown,
  MessageCircle
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import InvestmentPage from './InvestmentPage';
import WithdrawalSection from './WithdrawalSection';
import CustomerSupportModal from './CustomerSupportModal';
import './Dashboard.css';

const Dashboard = () => {
  const { t, i18n } = useTranslation();
  const { user, logout } = useAuth();
  const [showBalance, setShowBalance] = useState(true);
  const [isInvestmentPageOpen, setIsInvestmentPageOpen] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isDarkMode, setIsDarkMode] = useState(() => {
    return localStorage.getItem('theme') === 'dark';
  });
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [isSupportModalOpen, setIsSupportModalOpen] = useState(false);

  // Matrix Rain Effect - Sonsuzluktan yağmur gibi yağan rastgele kolonlar
  const matrixData = useMemo(() => {
    const chars = ['0', '1', '$', '€', '£', '¥', '₺', '₽', '₿', 'Ξ', '₮', '+', '-', '=', '%', '#', '@', '*', '&', '|', '/', '\\', '<', '>', '{', '}', '[', ']'];
    const columns = [];

    // 100 kolon - daha yoğun yağmur efekti
    for (let i = 0; i < 100; i++) {
      const columnChars = [];
      const charCount = 12 + Math.floor(Math.random() * 8); // 12-20 karakter arası

      for (let j = 0; j < charCount; j++) {
        columnChars.push({
          char: chars[Math.floor(Math.random() * chars.length)],
          animationDelay: j * 0.1 + Math.random() * 0.5, // Daha yavaş karakter görünümü
          opacity: Math.random() * 0.4 + 0.6
        });
      }

      // Aktif akan animasyon - her kolon farklı aşamada
      const animationDuration = 25 + Math.random() * 15; // 25-40s
      const currentProgress = Math.random(); // 0-1 arası (animasyonun hangi aşamasında)

      columns.push({
        id: i,
        left: Math.random() * 98, // Rastgele yatay pozisyon
        animationDelay: -(currentProgress * animationDuration), // Negatif delay = zaten başlamış
        animationDuration: animationDuration,
        chars: columnChars,
        // Mevcut pozisyonu hesapla (animasyon zaten devam ediyor)
        currentPosition: -50 + (currentProgress * 200) // -50vh'den +150vh'ye kadar
      });
    }

    return columns;
  }, []); // Empty dependency array - only calculate once

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    // Apply theme on mount and when changed
    document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');
  }, [isDarkMode]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showLanguageDropdown && !event.target.closest('.language-selector')) {
        setShowLanguageDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showLanguageDropdown]);

  const toggleTheme = () => {
    const newTheme = !isDarkMode;
    setIsDarkMode(newTheme);
    localStorage.setItem('theme', newTheme ? 'dark' : 'light');
    document.documentElement.setAttribute('data-theme', newTheme ? 'dark' : 'light');
  };



  const changeLanguage = (lang) => {
    i18n.changeLanguage(lang);
    setShowLanguageDropdown(false);
  };

  // Language options (doğru bayrak yolları)
  const languages = [
    { code: 'tr', name: 'Türkçe', flag: '/flags/turkey.png' },
    { code: 'en', name: 'English', flag: '/flags/usa.png' },
    { code: 'ru', name: 'Русский', flag: '/flags/russia.png' }
  ];

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString(t('common.locale'), {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleLogout = () => {
    logout();
  };

  const renderNoInvestmentState = () => (
    <div className="no-investment-state">
      <div className="no-investment-icon">
        <Package size={64} />
      </div>
      <h3>{t('dashboard.no_investment.title')}</h3>
      <p>{t('dashboard.no_investment.description')}</p>
      <button
        className="btn btn-primary btn-lg"
        onClick={() => setIsInvestmentPageOpen(true)}
      >
        <CreditCard size={20} />
        {t('dashboard.no_investment.cta')}
      </button>
    </div>
  );

  const renderInvestmentCards = () => {
    if (!user.hasInvestment || !user.investments?.length) {
      return renderNoInvestmentState();
    }

    return user.investments.map((investment) => (
      <div key={investment.id} className="investment-card">
        <div className="investment-header">
          <div className="investment-package">
            <PieChart size={24} />
            <div>
              <h4>{investment.package}</h4>
              <div className="package-details">
                <span className="investment-status">{t(`dashboard.status.${investment.status}`)}</span>
                <span className="package-type">
                  {investment.isLocked ? '🔒 ' : '🔓 '}
                  {investment.packageType === 'vip' ? 'VIP' :
                   investment.packageType === 'locked' ? t('plans.locked.title') :
                   t('plans.flexible.title')}
                </span>
              </div>
            </div>
          </div>
          <div className="investment-amount-container">
            <button
              className="balance-toggle-inline"
              onClick={() => setShowBalance(!showBalance)}
              title={showBalance ? t('dashboard.hide_balance') : t('dashboard.show_balance')}
            >
              {showBalance ? <Eye size={16} /> : <EyeOff size={16} />}
            </button>
            <div className="investment-amount">
              {showBalance ? formatCurrency(investment.amount) : '••••••'}
            </div>
          </div>
        </div>
        
        <div className="investment-stats">
          <div className="stat-item">
            <span className="stat-label">{t('dashboard.daily_earnings')}</span>
            <span className="stat-value positive">
              <ArrowUpRight size={16} />
              {showBalance ? formatCurrency(investment.dailyEarnings) : '••••'}
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">{t('dashboard.total_earnings')}</span>
            <span className="stat-value positive">
              <TrendingUp size={16} />
              {showBalance ? formatCurrency(investment.totalEarnings) : '••••••'}
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">{t('dashboard.current_return')}</span>
            <span className="stat-value positive">
              {investment.currentReturn}%
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">APR</span>
            <span className="stat-value highlight">
              {investment.expectedReturn}%
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">{t('plans.monthly')}</span>
            <span className="stat-value positive">
              {investment.monthlyRate}%
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">{t('dashboard.duration')}</span>
            <span className="stat-value">
              {investment.duration} {t('common.months')}
            </span>
          </div>
        </div>

        <div className="investment-progress">
          <div className="progress-header">
            <span>{t('dashboard.progress')}</span>
            <span>{investment.currentReturn}% / {investment.expectedReturn}%</span>
          </div>
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: `${(investment.currentReturn / investment.expectedReturn) * 100}%` }}
            ></div>
          </div>
        </div>

        <div className="investment-footer">
          <span className="start-date">
            <Calendar size={16} />
            {t('dashboard.start_date')}: {formatDate(investment.startDate)}
          </span>
        </div>

        {/* Çekim Sistemi - Sadece esnek paketler için */}
        {investment.packageType === 'flexible' && (
          <WithdrawalSection
            investment={investment}
            showBalance={showBalance}
          />
        )}
      </div>
    ));
  };

  return (
    <div className="dashboard">
      {/* Matrix Rain Background */}
      <div className="matrix-rain">
        {matrixData.map((column) => (
          <div
            key={column.id}
            className="matrix-column"
            style={{
              left: `${column.left}%`,
              animationDelay: `${column.animationDelay}s`,
              animationDuration: `${column.animationDuration}s`,
              transform: `translate3d(0, ${column.currentPosition}vh, 0)` // Mevcut pozisyon
            }}
          >
            {column.chars.map((charData, j) => (
              <span
                key={j}
                className="matrix-char"
                style={{
                  animationDelay: `${charData.animationDelay + Math.random() * 1}s`,
                  opacity: charData.opacity,
                  animationDuration: `${2 + Math.random() * 2}s` // Daha yavaş fade (2-4s)
                }}
              >
                {charData.char}
              </span>
            ))}
          </div>
        ))}
      </div>

      <div className="dashboard-header">
        <div className="container">
          <div className="header-content">
            <div className="user-info">
              <div className="user-avatar">
                <User size={24} />
              </div>
              <div className="user-details">
                <h2>{t('dashboard.welcome')}, {user?.name}</h2>
                <p className="user-email">{user?.email}</p>
              </div>
            </div>
            
            <div className="header-actions">
              <div className="live-time">
                <div className="live-indicator"></div>
                <span>{currentTime.toLocaleTimeString()}</span>
              </div>

              {/* Language Selector (ana sayfa tasarımı) */}
              <div className="language-selector">
                <button
                  onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
                  className="language-dropdown-trigger"
                  title={currentLanguage.name}
                >
                  <img src={currentLanguage.flag} alt={currentLanguage.code.toUpperCase()} className="flag-image" />
                  <ChevronDown
                    size={16}
                    className={`dropdown-arrow ${showLanguageDropdown ? 'open' : ''}`}
                  />
                </button>

                {showLanguageDropdown && (
                  <div className="language-dropdown">
                    {languages.map((language) => (
                      <button
                        key={language.code}
                        onClick={() => changeLanguage(language.code)}
                        className={`language-option ${i18n.language === language.code ? 'active' : ''}`}
                      >
                        <img src={language.flag} alt={language.code.toUpperCase()} className="flag-image" />
                        <span>{language.name}</span>
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Theme Toggle */}
              <button
                className="theme-toggle"
                onClick={toggleTheme}
                title={isDarkMode ? 'Light Mode' : 'Dark Mode'}
              >
                {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}
              </button>



              <button className="notification-btn">
                <Bell size={20} />
                <span className="notification-badge">3</span>
              </button>

              <button className="settings-btn">
                <Settings size={20} />
              </button>

              <button
                className="logout-btn"
                onClick={handleLogout}
                title={t('dashboard.logout')}
              >
                <LogOut size={20} />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="dashboard-content">
        <div className="container">
          <div className="dashboard-grid">
            <div className="main-content">
              <div className="section-header">
                <h3>{t('dashboard.my_investments')}</h3>
                {user.hasInvestment && (
                  <button
                    className="btn btn-secondary"
                    onClick={() => setIsInvestmentPageOpen(true)}
                  >
                    <CreditCard size={16} />
                    {t('dashboard.new_investment')}
                  </button>
                )}
              </div>
              
              <div className="investments-container">
                {renderInvestmentCards()}
              </div>
            </div>

            <div className="sidebar">
              <div className="quick-stats">
                <h4>{t('dashboard.quick_stats')}</h4>
                <div className="stat-card">
                  <DollarSign size={20} />
                  <div>
                    <span className="stat-number">
                      {showBalance ? formatCurrency(user.investments?.reduce((sum, inv) => sum + inv.totalEarnings, 0) || 0) : '••••••'}
                    </span>
                    <span className="stat-label">{t('dashboard.total_earnings')}</span>
                  </div>
                </div>
                
                <div className="stat-card">
                  <TrendingUp size={20} />
                  <div>
                    <span className="stat-number">
                      {user.investments?.length || 0}
                    </span>
                    <span className="stat-label">{t('dashboard.active_investments')}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Footer */}
      <div className="dashboard-footer">
        <div className="container">
          <div className="footer-content">
            <p className="footer-text">{t('support.footer_text')}</p>
            <div className="footer-badges">
              <span className="footer-badge ssl">{t('support.ssl_secure')}</span>
              <span className="footer-badge usdt">{t('support.usdt_details')}</span>
              <span className="footer-badge support-24-7">{t('support.support_24_7')}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Customer Support Button */}
      <button
        className="floating-support-btn"
        onClick={() => setIsSupportModalOpen(true)}
        title={t('support.title')}
      >
        <MessageCircle size={24} />
      </button>

      {/* Investment Page */}
      {isInvestmentPageOpen && (
        <InvestmentPage
          onClose={() => setIsInvestmentPageOpen(false)}
        />
      )}

      {/* Customer Support Modal */}
      <CustomerSupportModal
        isOpen={isSupportModalOpen}
        onClose={() => setIsSupportModalOpen(false)}
      />
    </div>
  );
};

export default Dashboard;
