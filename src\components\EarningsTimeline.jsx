import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import './EarningsTimeline.css';

const EarningsTimeline = ({ amount, selectedPlan, selectedPeriod, setSelectedPeriod, rates, calculateEarnings, periods }) => {
  const { t } = useTranslation();
  const [animatedValues, setAnimatedValues] = useState({});

  // periods now comes from props - plan-specific periods

  // Calculate earnings for each year
  const timelineData = periods.map(year => {
    const earnings = calculateEarnings(amount, rates[selectedPlan], year);
    const totalAmount = amount + earnings;
    return {
      year,
      earnings,
      totalAmount,
      percentage: ((earnings / amount) * 100).toFixed(1)
    };
  });

  // Calculate progress width based on plan type and selected period
  const getProgressWidth = (selectedPeriod, totalPeriods) => {
    if (selectedPeriod === 0) return '0%';

    if (totalPeriods === 2) {
      // 2 Year Plan: 5% -> 15% -> 85%
      return selectedPeriod === 1 ? '15%' : '85%';
    } else if (totalPeriods === 3) {
      // 3 Year Plan: 5% -> 15% -> 50% -> 85%
      if (selectedPeriod === 1) return '15%';
      if (selectedPeriod === 2) return '50%';
      return '85%';
    } else if (totalPeriods === 4) {
      // 4 Year Plan: 5% -> 15% -> 40% -> 65% -> 85%
      if (selectedPeriod === 1) return '15%';
      if (selectedPeriod === 2) return '40%';
      if (selectedPeriod === 3) return '65%';
      return '85%';
    }

    return '0%';
  };

  // Reset selectedPeriod when plan changes
  useEffect(() => {
    setSelectedPeriod(0);
  }, [selectedPlan, setSelectedPeriod]);

  // Animate values when they change
  useEffect(() => {
    const timer = setTimeout(() => {
      const newAnimatedValues = {};
      timelineData.forEach(item => {
        newAnimatedValues[item.year] = item.earnings;
      });
      setAnimatedValues(newAnimatedValues);
    }, 100);

    return () => clearTimeout(timer);
  }, [amount, selectedPlan]);

  return (
    <div className="earnings-timeline">
      <div className="timeline-container">
        {/* Timeline line */}
        <div className="timeline-line">
          <div
            className="timeline-progress"
            style={{
              width: getProgressWidth(selectedPeriod, periods.length),
              left: '0%'
            }}
          />
        </div>

        {/* Hidden starting point (0 point) */}
        <div className="timeline-start-point"></div>

        {/* Timeline milestones */}
        <div className="timeline-milestones">
          {timelineData.map((item, index) => {
            const isActive = selectedPeriod > 0 && item.year <= selectedPeriod;
            const isSelected = item.year === selectedPeriod;

            return (
              <div
                key={item.year}
                className={`timeline-milestone ${isActive ? 'active' : ''} ${isSelected ? 'selected' : ''}`}
                onClick={() => setSelectedPeriod(item.year)}
              >
                {/* Milestone point */}
                <div className="milestone-point">
                  <div className="milestone-inner">
                    {isActive && <div className="milestone-pulse" />}
                  </div>
                </div>

                {/* Milestone content */}
                <div className="milestone-content">
                  <div className="milestone-year">
                    {item.year}. {item.year === 1 ? t('calculator.year') : t('calculator.years')}
                  </div>

                  {isActive && (
                    <div className="milestone-details">
                      <div className="milestone-earnings">
                        +{item.earnings.toLocaleString('en-US', {
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 0
                        })}
                        <img src="/icons/tether.png" alt="USDT" className="milestone-usdt-icon" />
                      </div>
                      <div className="milestone-percentage">
                        +{item.percentage}%
                      </div>
                      <div className="milestone-total">
                        <div className="milestone-total-label">
                          {t('calculator.total').toUpperCase()}
                        </div>
                        <div className="milestone-total-amount">
                          {item.totalAmount.toLocaleString('en-US', {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0
                          })}
                          <img src="/icons/tether.png" alt="USDT" className="milestone-total-icon" />
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Connecting line to next milestone */}
                {index < timelineData.length - 1 && (
                  <div className={`milestone-connector ${isActive ? 'active' : ''}`} />
                )}
              </div>
            );
          })}
        </div>

        {/* Interactive year buttons */}
        <div className="timeline-controls">
          {periods.map(year => (
            <button
              key={year}
              className={`timeline-control-btn ${selectedPeriod === year ? 'active' : ''}`}
              onClick={() => setSelectedPeriod(year)}
            >
              {year}. {year === 1 ? t('calculator.year') : t('calculator.years')}
            </button>
          ))}
        </div>
      </div>

      {/* Selected period details */}
      {selectedPeriod > 0 && (
        <div className="timeline-summary">
          <div className="summary-card">
            <div className="summary-header">
              <h4>{selectedPeriod}. {selectedPeriod === 1 ? t('calculator.year') : t('calculator.years')} {t('calculator.summary')}</h4>
            </div>
            <div className="summary-content">
              <div className="summary-item">
                <span className="summary-label">{t('calculator.initial_investment')}</span>
                <span className="summary-value">
                  {amount.toLocaleString('en-US')} USDT
                </span>
              </div>
              <div className="summary-item highlight">
                <span className="summary-label">{t('calculator.total_earnings')}</span>
                <span className="summary-value earnings">
                  +{timelineData.find(item => item.year === selectedPeriod)?.earnings.toLocaleString('en-US', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                  }) || '0'} USDT
                </span>
              </div>
              <div className="summary-item">
                <span className="summary-label">{t('calculator.final_amount')}</span>
                <span className="summary-value">
                  {timelineData.find(item => item.year === selectedPeriod)?.totalAmount.toLocaleString('en-US', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                  }) || '0'} USDT
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EarningsTimeline;