import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Calculator } from 'lucide-react';
import AnimatedBackground from './AnimatedBackground';
import EarningsTimeline from './EarningsTimeline';
import './APRCalculator.css';

const APRCalculator = () => {
  const { t } = useTranslation();
  const [amount, setAmount] = useState(1000);
  const [selectedPlan, setSelectedPlan] = useState('flexible');
  const [selectedPeriod, setSelectedPeriod] = useState(0); // 0'dan başla

  // APR rates
  const rates = {
    flexible: 78, // %78 APR
    locked: 102,  // %102 APR
    vip: 132      // %132 APR
  };

  // Calculate earnings based on compound interest
  const calculateEarnings = (principal, rate, years) => {
    const monthlyRate = rate / 100 / 12;
    const months = years * 12;
    const compoundAmount = principal * Math.pow(1 + monthlyRate, months);
    return compoundAmount - principal;
  };

  const earnings = selectedPeriod > 0 ? calculateEarnings(amount, rates[selectedPlan], selectedPeriod) : 0;
  const totalAmount = amount + earnings;

  // Plan-specific maximum periods
  const getMaxPeriods = (planType) => {
    switch(planType) {
      case 'vip': return [1, 2]; // VIP: Max 2 years
      case 'locked': return [1, 2, 3]; // Locked: Max 3 years
      case 'flexible': return [1, 2, 3, 4]; // Flexible: Max 4 years
      default: return [1, 2, 3, 4];
    }
  };

  const periods = getMaxPeriods(selectedPlan);

  // Reset selectedPeriod if it exceeds the new plan's maximum
  useEffect(() => {
    const maxPeriod = Math.max(...periods);
    if (selectedPeriod > maxPeriod) {
      setSelectedPeriod(0); // Reset to 0 instead of max
    }
  }, [selectedPlan, periods, selectedPeriod]);

  const handleSubscribeNow = () => {
    // Hero section'daki kayıt alanına yönlendir
    const heroSection = document.getElementById('home');
    if (heroSection) {
      const headerHeight = 80; // Header yüksekliği
      const targetPosition = heroSection.offsetTop - headerHeight;

      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });

      // AuthCard'ı highlight et
      setTimeout(() => {
        const authCard = document.querySelector('.auth-card');
        if (authCard) {
          authCard.style.boxShadow = 'var(--shadow-xl), 0 0 60px rgba(99, 102, 241, 0.3)';
          setTimeout(() => {
            authCard.style.boxShadow = 'var(--shadow-xl), 0 0 40px rgba(99, 102, 241, 0.1)';
          }, 2000);
        }
      }, 800);
    }
  };

  return (
    <section id="calculator" className="apr-calculator-section">
      <div className="calculator-background">
        <div className="calculator-pattern"></div>
        <div className="calculator-gradient"></div>
        <AnimatedBackground variant="financial" />
      </div>
      <div className="container">
        <div className="calculator-header">
          <div className="calculator-title-row">
            <div className="calculator-icon">
              <Calculator className="icon" />
            </div>
            <h2 className="calculator-title" dangerouslySetInnerHTML={{ __html: t('calculator.title') }}>
            </h2>
          </div>
          <p className="calculator-subtitle">
            {t('calculator.subtitle')}
          </p>
        </div>

        <div className="calculator-content">
          <div className="calculator-inputs">
            {/* Financial animation for calculator inputs area */}
            <AnimatedBackground variant="financial" className="calculator-inputs-bg" />
            <div className="input-row">
              <span className="input-text">{t('calculator.amount_label')}</span>
              <div className="amount-input">
                <input
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(Number(e.target.value))}
                  min="100"
                  step="100"
                  className="amount-field"
                />
                <span className="currency-dropdown">
                  <img src="/icons/tether.png" alt="USDT" className="currency-icon-img" />
                  <span className="currency-label">USDT</span>
                </span>
              </div>
              <span className="input-text">{t('calculator.plan_label')}</span>
              <div className="plan-selector">
                <button
                  className={`plan-btn ${selectedPlan === 'flexible' ? 'active' : ''}`}
                  onClick={() => setSelectedPlan('flexible')}
                >
                  {t('calculator.flexible')}
                </button>
                <button
                  className={`plan-btn ${selectedPlan === 'locked' ? 'active' : ''}`}
                  onClick={() => setSelectedPlan('locked')}
                >
                  {t('calculator.locked')}
                </button>
                <button
                  className={`plan-btn vip-plan-btn ${selectedPlan === 'vip' ? 'active' : ''}`}
                  onClick={() => setSelectedPlan('vip')}
                >
                  {t('calculator.vip')}
                </button>
              </div>
              <span className="input-text">{t('calculator.investment')}.</span>
            </div>

            <div className="products-section">
              {/* Geometric animation for products section */}
              <AnimatedBackground variant="geometric" className="products-section-bg" />
              <h3>{t('calculator.products_offer')}</h3>
              <div className="product-card">
                <div className="product-header">
                  <span className="product-name">{t('calculator.simple_earn')}</span>
                  <span className="product-badge">{t('calculator.low_risk')}</span>
                </div>
                <div className="product-rate">
                  {rates[selectedPlan]}% APR
                </div>
                <div className="product-breakdown">
                  {t('calculator.apr_breakdown')}
                </div>
                <button className="subscribe-btn" onClick={handleSubscribeNow}>
                  {t('calculator.subscribe_now')}
                </button>
              </div>
            </div>
          </div>

          <EarningsTimeline
            amount={amount}
            selectedPlan={selectedPlan}
            selectedPeriod={selectedPeriod}
            setSelectedPeriod={setSelectedPeriod}
            rates={rates}
            calculateEarnings={calculateEarnings}
            periods={periods}
          />
        </div>

        <div className="calculator-disclaimer">
          <p>{t('calculator.disclaimer')}</p>
        </div>
      </div>
    </section>
  );
};

export default APRCalculator;
