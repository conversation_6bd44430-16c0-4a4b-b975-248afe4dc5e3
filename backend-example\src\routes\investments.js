import express from 'express';
import { authenticateToken, requireVerifiedEmail } from '../middleware/auth.js';
import { validateCreateInvestment, validatePagination, validateObjectId } from '../middleware/validation.js';
import Investment from '../models/Investment.js';
import User from '../models/User.js';

const router = express.Router();

// Tüm route'lar authentication gerektirir
router.use(authenticateToken);

// Yeni yatırım oluştur
router.post('/', requireVerifiedEmail, validateCreateInvestment, async (req, res) => {
  try {
    const {
      packageType,
      amount,
      network,
      walletAddress,
      transactionHash,
      notes
    } = req.body;

    // Paket bilgilerini ayarla
    const packageConfig = {
      starter: { aprRate: 102, lockPeriod: 365, minAmount: 100, maxAmount: 10000 },
      growth: { aprRate: 108, lockPeriod: 365, minAmount: 5000, maxAmount: 50000 },
      premium: { aprRate: 115, lockPeriod: 365, minAmount: 25000, maxAmount: 100000 },
      enterprise: { aprRate: 125, lockPeriod: 365, minAmount: 100000, maxAmount: 1000000 }
    };

    const config = packageConfig[packageType];
    if (!config) {
      return res.status(400).json({
        success: false,
        message: 'Geçersiz paket türü'
      });
    }

    // Tutar kontrolü
    if (amount < config.minAmount || amount > config.maxAmount) {
      return res.status(400).json({
        success: false,
        message: `${packageType} paketi için tutar ${config.minAmount}-${config.maxAmount} USDT arasında olmalıdır`
      });
    }

    // Cüzdan adresi kontrolü (network'e göre)
    const walletValidation = {
      TRC20: /^T[A-Za-z1-9]{33}$/,
      ERC20: /^0x[a-fA-F0-9]{40}$/,
      BEP20: /^0x[a-fA-F0-9]{40}$/
    };

    if (!walletValidation[network].test(walletAddress)) {
      return res.status(400).json({
        success: false,
        message: `Geçersiz ${network} cüzdan adresi`
      });
    }

    // Yatırım oluştur
    const investment = new Investment({
      user: req.user.userId,
      packageType,
      amount,
      currency: 'USDT',
      network,
      walletAddress,
      transactionHash,
      aprRate: config.aprRate,
      lockPeriod: config.lockPeriod,
      notes,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    await investment.save();

    res.status(201).json({
      success: true,
      message: 'Yatırım başarıyla oluşturuldu',
      data: {
        investment: {
          id: investment._id,
          packageType: investment.packageType,
          amount: investment.amount,
          currency: investment.currency,
          network: investment.network,
          walletAddress: investment.walletAddress,
          status: investment.status,
          aprRate: investment.aprRate,
          lockPeriod: investment.lockPeriod,
          expectedReturn: investment.expectedReturn,
          createdAt: investment.createdAt
        }
      }
    });

  } catch (error) {
    console.error('Create investment error:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Bu transaction hash zaten kullanılmış'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
});

// Kullanıcının yatırımlarını listele
router.get('/', validatePagination, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status;
    const packageType = req.query.packageType;

    // Filter oluştur
    const filter = { user: req.user.userId };
    if (status) filter.status = status;
    if (packageType) filter.packageType = packageType;

    // Yatırımları getir
    const investments = await Investment.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Toplam sayı
    const total = await Investment.countDocuments(filter);

    // Her yatırım için güncel getiriyi hesapla
    const investmentsWithReturns = investments.map(investment => {
      let currentReturn = 0;
      if (investment.status === 'active' && investment.startDate) {
        const now = new Date();
        const start = new Date(investment.startDate);
        const daysElapsed = Math.floor((now - start) / (1000 * 60 * 60 * 24));
        const dailyRate = investment.aprRate / 365 / 100;
        currentReturn = Math.round(investment.amount * dailyRate * daysElapsed * 100) / 100;
      }

      return {
        ...investment,
        currentReturn,
        progressPercentage: investment.startDate && investment.endDate ? 
          Math.min(100, Math.round(((new Date() - new Date(investment.startDate)) / 
          (new Date(investment.endDate) - new Date(investment.startDate))) * 100)) : 0
      };
    });

    res.status(200).json({
      success: true,
      data: {
        investments: investmentsWithReturns,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('List investments error:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
});

// Yatırım detayı getir
router.get('/:id', validateObjectId('id'), async (req, res) => {
  try {
    const investment = await Investment.findOne({
      _id: req.params.id,
      user: req.user.userId
    }).lean();

    if (!investment) {
      return res.status(404).json({
        success: false,
        message: 'Yatırım bulunamadı'
      });
    }

    // Güncel getiriyi hesapla
    let currentReturn = 0;
    if (investment.status === 'active' && investment.startDate) {
      const now = new Date();
      const start = new Date(investment.startDate);
      const daysElapsed = Math.floor((now - start) / (1000 * 60 * 60 * 24));
      const dailyRate = investment.aprRate / 365 / 100;
      currentReturn = Math.round(investment.amount * dailyRate * daysElapsed * 100) / 100;
    }

    const investmentWithReturn = {
      ...investment,
      currentReturn,
      progressPercentage: investment.startDate && investment.endDate ? 
        Math.min(100, Math.round(((new Date() - new Date(investment.startDate)) / 
        (new Date(investment.endDate) - new Date(investment.startDate))) * 100)) : 0,
      daysRemaining: investment.endDate ? 
        Math.max(0, Math.ceil((new Date(investment.endDate) - new Date()) / (1000 * 60 * 60 * 24))) : null
    };

    res.status(200).json({
      success: true,
      data: { investment: investmentWithReturn }
    });

  } catch (error) {
    console.error('Get investment error:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
});

// Yatırım durumu güncelle (Admin için)
router.put('/:id/status', validateObjectId('id'), async (req, res) => {
  try {
    const { status, note } = req.body;

    if (!['pending', 'confirmed', 'active', 'completed', 'cancelled', 'failed'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Geçersiz durum'
      });
    }

    const investment = await Investment.findOne({
      _id: req.params.id,
      user: req.user.userId
    });

    if (!investment) {
      return res.status(404).json({
        success: false,
        message: 'Yatırım bulunamadı'
      });
    }

    // Sadece admin veya belirli durumlar için güncelleme yapılabilir
    if (req.user.role !== 'admin' && !['cancelled'].includes(status)) {
      return res.status(403).json({
        success: false,
        message: 'Bu işlem için yetkiniz yok'
      });
    }

    investment.updateStatus(status, note, req.user.userId);
    await investment.save();

    res.status(200).json({
      success: true,
      message: 'Yatırım durumu güncellendi',
      data: {
        investment: {
          id: investment._id,
          status: investment.status,
          statusHistory: investment.statusHistory
        }
      }
    });

  } catch (error) {
    console.error('Update investment status error:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
});

// Yatırım istatistikleri
router.get('/stats/summary', async (req, res) => {
  try {
    const userId = req.user.userId;

    const stats = await Investment.aggregate([
      { $match: { user: userId } },
      {
        $group: {
          _id: null,
          totalInvested: {
            $sum: {
              $cond: [
                { $in: ['$status', ['active', 'completed']] },
                '$amount',
                0
              ]
            }
          },
          totalEarnings: {
            $sum: {
              $cond: [
                { $eq: ['$status', 'completed'] },
                '$totalReturn',
                0
              ]
            }
          },
          activeInvestments: {
            $sum: {
              $cond: [{ $eq: ['$status', 'active'] }, 1, 0]
            }
          },
          completedInvestments: {
            $sum: {
              $cond: [{ $eq: ['$status', 'completed'] }, 1, 0]
            }
          },
          pendingInvestments: {
            $sum: {
              $cond: [{ $eq: ['$status', 'pending'] }, 1, 0]
            }
          }
        }
      }
    ]);

    const result = stats[0] || {
      totalInvested: 0,
      totalEarnings: 0,
      activeInvestments: 0,
      completedInvestments: 0,
      pendingInvestments: 0
    };

    res.status(200).json({
      success: true,
      data: { stats: result }
    });

  } catch (error) {
    console.error('Investment stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
});

export default router;
