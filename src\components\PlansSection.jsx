import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Zap, Lock, ArrowRight } from 'lucide-react';
import { CryptoWallet, VaultLock, VIPCrown } from './FinancialIcons';
import AnimatedBackground from './AnimatedBackground';
import './PlansSection.css';

const PlansSection = () => {
  const { t } = useTranslation();

  // VIP plan member count state (simulated)
  const [vipMembers, setVipMembers] = useState(7); // 7 out of 10 spots taken

  const handlePlanSelect = (planType) => {
    // Hero section'daki kayıt alanına yönlendir
    const heroSection = document.getElementById('home');
    if (heroSection) {
      const headerHeight = 80; // Header yüksekliği
      const targetPosition = heroSection.offsetTop - headerHeight;

      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });

      // AuthCard'ı register moduna geçir (eğer login modundaysa)
      setTimeout(() => {
        const authCard = document.querySelector('.auth-card');
        if (authCard) {
          // AuthCard'a focus ver ve hafif bir highlight efekti ekle
          authCard.style.boxShadow = 'var(--shadow-xl), 0 0 60px rgba(99, 102, 241, 0.3)';
          setTimeout(() => {
            authCard.style.boxShadow = 'var(--shadow-xl), 0 0 40px rgba(99, 102, 241, 0.1)';
          }, 2000);
        }
      }, 800);
    }
  };

  const plans = [
    {
      id: 'flexible',
      title: t('plans.flexible.title'),
      icon: <CryptoWallet className="plan-icon-svg" />,
      monthly: t('plans.flexible.monthly'),
      yearly: t('plans.flexible.yearly'),
      description: t('plans.flexible.description'),
      cta: t('plans.flexible.cta'),
      highlight: false,
      features: [
        t('plans.features.flexible.withdraw'),
        t('plans.features.flexible.monthly'),
        t('plans.features.flexible.minimum'),
        t('plans.features.flexible.support')
      ]
    },
    {
      id: 'locked',
      title: t('plans.locked.title'),
      icon: <VaultLock className="plan-icon-svg" />,
      monthly: t('plans.locked.monthly'),
      yearly: t('plans.locked.yearly'),
      description: t('plans.locked.description'),
      cta: t('plans.locked.cta'),
      highlight: true,
      features: [
        t('plans.features.locked.period'),
        t('plans.features.locked.guarantee'),
        t('plans.features.locked.minimum'),
        t('plans.features.locked.priority')
      ]
    },
    {
      id: 'vip',
      title: t('plans.vip.title'),
      icon: <VIPCrown className="plan-icon-svg vip-crown" />,
      monthly: t('plans.vip.monthly'),
      yearly: t('plans.vip.yearly'),
      description: t('plans.vip.description'),
      cta: t('plans.vip.cta'),
      highlight: false,
      isVip: true,
      features: [
        t('plans.features.vip.period'),
        t('plans.features.vip.advisor'),
        t('plans.features.vip.minimum'),
        t('plans.features.vip.payout')
      ]
    }
  ];

  return (
    <section id="plans" className="plans-section section">
      <AnimatedBackground variant="particles" />
      <div className="container">
        <div className="section-header text-center">
          <h2 className="section-title text-shimmer fade-in-up">
            {t('plans.title')}
          </h2>
          <p className="section-subtitle fade-in-up">
            {t('plans.subtitle')}
          </p>
        </div>

        <div className="plans-grid">
          {plans.map((plan, index) => (
            <div
              key={plan.id}
              className={`plan-card ${plan.highlight ? 'highlight' : ''} ${plan.isVip ? 'vip-card' : ''} fade-in-up`}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {plan.highlight && (
                <div className="popular-badge">
                  {t('plans.locked.badge')}
                </div>
              )}

              {plan.isVip && (
                <div className="vip-badge">
                  {t('plans.vip.badge')}
                </div>
              )}

              <div className="plan-header">
                {plan.icon}
                <h3 className="plan-title">{plan.title}</h3>
              </div>

              <div className="plan-pricing">
                <div className="pricing-item">
                  <span className="pricing-label">{t('plans.monthly')}</span>
                  <span className="pricing-value">{plan.monthly.split(': ')[1]}</span>
                </div>
                <div className="pricing-item main">
                  <span className="pricing-label">{t('plans.yearly')}</span>
                  <span className="pricing-value text-gradient">
                    {plan.yearly.split(': ')[1]} <img src="/icons/tether.png" alt="USDT" className="plan-usdt-icon" />
                  </span>
                </div>
              </div>

              {plan.isVip && (
                <div className="vip-progress">
                  <div className="vip-progress-label">
                    <span>{t('plans.vip.members')}: {vipMembers}/10</span>
                    <span className="remaining">{10 - vipMembers} {t('plans.vip.remaining')}</span>
                  </div>
                  <div className="vip-progress-bar">
                    <div
                      className="vip-progress-fill"
                      style={{ width: `${(vipMembers / 10) * 100}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <p className="plan-description">
                {plan.description}
              </p>

              <ul className="plan-features">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="feature-item">
                    <ArrowRight className="feature-icon" />
                    <span>
                      {feature}
                      {feature.includes('1000') || feature.includes('2500') || feature.includes('5000') ? (
                        <img src="/icons/tether.png" alt="USDT" className="feature-usdt-icon" />
                      ) : null}
                    </span>
                  </li>
                ))}
              </ul>

              <button
                className={`btn ${plan.highlight ? 'btn-primary' : plan.isVip ? 'btn-vip' : 'btn-secondary'} btn-lg plan-cta`}
                onClick={() => handlePlanSelect(plan.id)}
                disabled={plan.isVip && vipMembers >= 10}
              >
                {plan.isVip && vipMembers >= 10 ? t('plans.vip.sold_out') : plan.cta}
              </button>
            </div>
          ))}
        </div>

        <div className="plans-note">
          <p dangerouslySetInnerHTML={{ __html: t('plans.disclaimer') }}>
          </p>
        </div>
      </div>
    </section>
  );
};

export default PlansSection;
