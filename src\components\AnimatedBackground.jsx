import React from 'react';
import './AnimatedBackground.css';

const AnimatedBackground = ({ variant = 'default', className = '' }) => {
  const renderPattern = () => {
    switch (variant) {
      case 'financial':
        return (
          <div className="financial-pattern">
            {/* Floating financial symbols - Traditional Currencies */}
            <div className="symbol symbol-dollar">$</div>
            <div className="symbol symbol-euro">€</div>
            <div className="symbol symbol-pound">£</div>
            <div className="symbol symbol-yen">¥</div>
            <div className="symbol symbol-turkish-lira">₺</div>
            <div className="symbol symbol-ruble">₽</div>
            <div className="symbol symbol-rupee">₹</div>
            <div className="symbol symbol-won">₩</div>
            <div className="symbol symbol-franc">₣</div>

            {/* Floating crypto symbols */}
            <div className="symbol symbol-bitcoin">₿</div>
            <div className="symbol symbol-ethereum">Ξ</div>
            <div className="symbol symbol-usdt">₮</div>
            
            {/* Animated lines */}
            <div className="financial-line line-1"></div>
            <div className="financial-line line-2"></div>
            <div className="financial-line line-3"></div>
            

          </div>
        );
      
      case 'geometric':
        return (
          <div className="geometric-pattern">
            {/* Animated shapes */}
            <div className="shape triangle-1"></div>
            <div className="shape triangle-2"></div>
            <div className="shape circle-1"></div>
            <div className="shape circle-2"></div>
            <div className="shape square-1"></div>
            <div className="shape square-2"></div>
            
            {/* Grid lines */}
            <div className="grid-lines">
              <div className="grid-line horizontal-1"></div>
              <div className="grid-line horizontal-2"></div>
              <div className="grid-line vertical-1"></div>
              <div className="grid-line vertical-2"></div>
            </div>
          </div>
        );
      
      case 'particles':
        return (
          <div className="particles-pattern">
            {Array.from({ length: 80 }, (_, i) => (
              <div
                key={i}
                className={`particle particle-${i + 1}`}
                style={{
                  left: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 8}s`,
                  animationDuration: `${8 + Math.random() * 4}s`
                }}
              ></div>
            ))}
          </div>
        );
      
      default:
        return (
          <div className="default-pattern">
            <div className="gradient-orb orb-1"></div>
            <div className="gradient-orb orb-2"></div>
            <div className="gradient-orb orb-3"></div>
            <div className="wave-line wave-1"></div>
            <div className="wave-line wave-2"></div>
          </div>
        );
    }
  };

  return (
    <div className={`animated-background ${variant} ${className}`}>
      {renderPattern()}
    </div>
  );
};

export default AnimatedBackground;
