.financial-icon {
  width: 100%;
  height: 100%;
  max-width: 200px;
  max-height: 120px;
}

/* Chart animations */
.chart-line {
  stroke-dasharray: 300;
  stroke-dashoffset: 300;
  animation: drawLine 2s ease-in-out forwards;
}

@keyframes drawLine {
  to {
    stroke-dashoffset: 0;
  }
}

.data-point {
  opacity: 0;
  animation: fadeInPoint 0.5s ease-in-out forwards;
}

.data-point:nth-child(1) { animation-delay: 0.5s; }
.data-point:nth-child(2) { animation-delay: 0.8s; }
.data-point:nth-child(3) { animation-delay: 1.1s; }
.data-point:nth-child(4) { animation-delay: 1.4s; }

.data-point-highlight {
  animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes fadeInPoint {
  to {
    opacity: 1;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    opacity: 1;
    filter: drop-shadow(0 0 5px var(--accent-gold));
  }
  50% {
    opacity: 0.7;
    filter: drop-shadow(0 0 15px var(--accent-gold));
  }
}

/* Floating elements */
.floating-coin {
  animation: floatCoin 3s ease-in-out infinite;
}

.floating-coin:nth-child(odd) {
  animation-delay: 1s;
}

@keyframes floatCoin {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

.floating-element {
  animation: floatElement 4s ease-in-out infinite;
}

.floating-element:nth-child(odd) {
  animation-delay: 2s;
}

@keyframes floatElement {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) scale(1.2);
    opacity: 1;
  }
}

/* Trending arrow */
.trending-arrow {
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  animation: drawArrow 1.5s ease-in-out forwards;
}

@keyframes drawArrow {
  to {
    stroke-dashoffset: 0;
  }
}

/* Security shield */
.security-shield {
  animation: shieldPulse 3s ease-in-out infinite;
}

.central-node {
  animation: centralNodePulse 2s ease-in-out infinite;
}

.security-indicator {
  animation: securityIndicatorBlink 1.5s ease-in-out infinite;
}

.security-indicator:nth-child(1) { animation-delay: 0s; }
.security-indicator:nth-child(2) { animation-delay: 0.75s; }

@keyframes shieldPulse {
  0%, 100% {
    filter: drop-shadow(0 0 5px var(--accent-primary));
  }
  50% {
    filter: drop-shadow(0 0 20px var(--accent-primary));
  }
}

@keyframes centralNodePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.3);
  }
}

@keyframes securityIndicatorBlink {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* Network nodes */
.network-node {
  animation: networkPulse 2s ease-in-out infinite;
}

.network-node:nth-child(1) { animation-delay: 0s; }
.network-node:nth-child(2) { animation-delay: 0.5s; }
.network-node:nth-child(3) { animation-delay: 1s; }
.network-node:nth-child(4) { animation-delay: 1.5s; }

.network-node-center {
  animation: centerPulse 1.5s ease-in-out infinite;
}

@keyframes networkPulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.3);
  }
}

@keyframes centerPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    filter: drop-shadow(0 0 5px var(--accent-primary));
  }
  50% {
    opacity: 0.8;
    transform: scale(1.5);
    filter: drop-shadow(0 0 15px var(--accent-primary));
  }
}

/* Vault Lock animations */
.vault-body {
  animation: vaultGlow 3s ease-in-out infinite;
}

.lock-body {
  animation: lockPulse 2s ease-in-out infinite;
}

.lock-shackle {
  animation: shacklePulse 2.5s ease-in-out infinite;
}

.security-dot {
  animation: securityBlink 1.5s ease-in-out infinite;
}

.security-dot:nth-child(1) { animation-delay: 0s; }
.security-dot:nth-child(2) { animation-delay: 0.3s; }
.security-dot:nth-child(3) { animation-delay: 0.6s; }
.security-dot:nth-child(4) { animation-delay: 0.9s; }

.floating-security {
  animation: floatSecurity 4s ease-in-out infinite;
}

.floating-security:nth-child(odd) {
  animation-delay: 2s;
}

@keyframes vaultGlow {
  0%, 100% {
    filter: drop-shadow(0 0 5px var(--accent-primary));
  }
  50% {
    filter: drop-shadow(0 0 20px var(--accent-primary));
  }
}

@keyframes lockPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 3px var(--accent-gold));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 0 10px var(--accent-gold));
  }
}

@keyframes shacklePulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes securityBlink {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

@keyframes floatSecurity {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-8px) scale(1.2);
    opacity: 1;
  }
}

/* Responsive sizing */
@media (max-width: 768px) {
  .financial-icon {
    max-width: 150px;
    max-height: 90px;
  }
}

/* VIP Crown animations */
.crown-base, .crown-spikes {
  animation: crownGlow 2s ease-in-out infinite alternate;
}

@keyframes crownGlow {
  0% {
    filter: drop-shadow(0 0 5px #FFD700);
  }
  100% {
    filter: drop-shadow(0 0 15px #FFD700) drop-shadow(0 0 25px #FFA500);
  }
}

.center-gem {
  animation: gemPulse 1.5s ease-in-out infinite;
}

@keyframes gemPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

.side-gem {
  animation: sideGemFloat 2s ease-in-out infinite;
}

.side-gem:nth-child(1) { animation-delay: 0.5s; }
.side-gem:nth-child(2) { animation-delay: 1s; }

@keyframes sideGemFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

.vip-text {
  animation: vipTextGlow 2s ease-in-out infinite alternate;
}

@keyframes vipTextGlow {
  0% {
    filter: drop-shadow(0 0 3px #FFD700);
  }
  100% {
    filter: drop-shadow(0 0 8px #FFD700) drop-shadow(0 0 12px #FFA500);
  }
}

.sparkle {
  animation: sparkleAnimation 3s ease-in-out infinite;
}

.sparkle-1 { animation-delay: 0s; }
.sparkle-2 { animation-delay: 0.5s; }
.sparkle-3 { animation-delay: 1s; }
.sparkle-4 { animation-delay: 1.5s; }

@keyframes sparkleAnimation {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

@media (max-width: 480px) {
  .financial-icon {
    max-width: 120px;
    max-height: 72px;
  }
}
