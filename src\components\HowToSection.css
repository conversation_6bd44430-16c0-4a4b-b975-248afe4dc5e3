.howto-section {
  background-color: var(--bg-primary);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 80px 0;
}

.steps-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 2rem;
  margin-bottom: 4rem;
  position: relative;
  z-index: 2;
}

.step-card {
  position: relative;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 1.5rem;
  padding: 2rem;
  max-width: 300px;
  text-align: center;
  transition: all 0.3s ease;
  flex: 1;
}

.step-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--accent-primary);
}

.step-number {
  position: absolute;
  top: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.125rem;
  box-shadow: var(--shadow-md);
}

.step-content {
  margin-top: 1rem;
}

.step-header {
  margin-bottom: 1.5rem;
}

.step-icon {
  width: 3rem;
  height: 3rem;
  color: var(--accent-primary);
  margin-bottom: 1rem;
}

.step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.step-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.step-connector {
  position: absolute;
  top: 50%;
  right: -2rem;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  z-index: 1;
}

.connector-line {
  width: 2rem;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  border-radius: 1px;
}

.connector-arrow {
  color: var(--accent-primary);
  font-size: 1.5rem;
  font-weight: 700;
  margin-left: -0.25rem;
}

.howto-cta {
  margin-top: 3rem;
}

.cta-note {
  margin-top: 1rem;
  font-size: 0.875rem;
  color: var(--text-muted);
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .steps-container {
    flex-direction: column;
    align-items: center;
    gap: 3rem;
  }
  
  .step-card {
    max-width: 400px;
    width: 100%;
  }
  
  .step-connector {
    position: static;
    transform: none;
    justify-content: center;
    margin: 1rem 0;
  }
  
  .connector-line {
    width: 2px;
    height: 2rem;
    background: linear-gradient(180deg, var(--accent-primary), var(--accent-secondary));
  }
  
  .connector-arrow {
    transform: rotate(90deg);
    margin-left: 0;
    margin-top: -0.25rem;
  }
}

@media (max-width: 768px) {
  .step-card {
    padding: 1.5rem;
    max-width: none;
  }
  
  .step-number {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }
  
  .step-icon {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .step-title {
    font-size: 1.125rem;
  }
  
  .step-description {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .step-card {
    padding: 1.25rem;
    margin: 0 -0.5rem;
  }
  
  .step-number {
    width: 2rem;
    height: 2rem;
    font-size: 0.875rem;
  }
  
  .step-icon {
    width: 2rem;
    height: 2rem;
  }
  
  .step-title {
    font-size: 1rem;
  }
  
  .step-description {
    font-size: 0.8rem;
  }
  
  .connector-line {
    height: 1.5rem;
  }
  
  .connector-arrow {
    font-size: 1.25rem;
  }
}
