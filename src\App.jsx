import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Sun, Moon, MessageCircle, ChevronDown, Menu, X } from 'lucide-react';
import { AuthProvider, useAuth } from './context/AuthContext';
import HeroSection from './components/HeroSection';
import ComparisonSection from './components/ComparisonSection';
import PlansSection from './components/PlansSection';
import APRCalculator from './components/APRCalculator';
import TrustSection from './components/TrustSection';
import HowToSection from './components/HowToSection';
import Footer from './components/Footer';
import ContactModal from './components/ContactModal';
import SEOHead from './components/SEOHead';
import Dashboard from './components/Dashboard';
import { useToast } from './components/Toast';
import { useActiveSection } from './hooks/useActiveSection';
import './App.css';

const AppContent = () => {
  const { i18n, t } = useTranslation();
  const { isAuthenticated, loading } = useAuth();
  const [theme, setTheme] = useState('light');
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { showSuccess, showInfo, ToastContainer } = useToast();
  const activeSection = useActiveSection();

  // Language options
  const languages = [
    { code: 'tr', name: 'Türkçe', flag: '/flags/turkey.png' },
    { code: 'en', name: 'English', flag: '/flags/usa.png' },
    { code: 'ru', name: 'Русский', flag: '/flags/russia.png' }
  ];

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];

  useEffect(() => {
    // Check for saved theme preference or default to 'light'
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
    document.documentElement.setAttribute('data-theme', savedTheme);
  }, []);

  // Cleanup body overflow on unmount
  useEffect(() => {
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
    document.documentElement.setAttribute('data-theme', newTheme);
  };

  const toggleLanguageDropdown = () => {
    setIsLanguageDropdownOpen(!isLanguageDropdownOpen);
  };

  const toggleMobileMenu = () => {
    const newState = !isMobileMenuOpen;
    setIsMobileMenuOpen(newState);

    // Body scroll'u kontrol et
    if (newState) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
  };

  const changeLanguage = (lng) => {
    const languageMessages = {
      'tr': 'Dil Türkçe olarak değiştirildi',
      'en': 'Language changed to English',
      'ru': 'Язык изменен на русский'
    };

    i18n.changeLanguage(lng);
    showInfo(languageMessages[lng], 3000);
    setIsLanguageDropdownOpen(false); // Close dropdown after selection
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isLanguageDropdownOpen && !event.target.closest('.language-selector')) {
        setIsLanguageDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isLanguageDropdownOpen]);

  const handleNavClick = (e, targetId) => {
    e.preventDefault();
    const targetElement = document.getElementById(targetId);
    if (targetElement) {
      // Mobile ve desktop için farklı offset değerleri
      const isMobile = window.innerWidth <= 768;
      let offset = isMobile ? 70 : 80; // Mobile için daha az offset

      switch(targetId) {
        case 'home':
          // Ana sayfaya tıklandığında en üste git
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
          return; // Erken çık, normal offset hesaplaması yapma
        case 'comparison':
          offset = isMobile ? 80 : 100; // Mobile için daha az offset
          break;
        case 'plans':
          offset = isMobile ? 80 : 100; // Mobile için daha az offset
          break;
        case 'calculator':
          offset = isMobile ? 120 : 200; // Mobile için daha az offset
          break;
        case 'trust':
          offset = isMobile ? 150 : 250; // Mobile için daha az offset
          break;
        case 'howto':
          offset = isMobile ? 160 : 280; // Mobile için daha az offset
          break;
        default:
          offset = isMobile ? 80 : 100;
      }

      const targetPosition = targetElement.offsetTop - offset;

      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
    }
    // Mobile menu'yu kapat ve scroll'u geri aç
    if (isMobileMenuOpen) {
      setIsMobileMenuOpen(false);
      document.body.style.overflow = 'unset';
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="loading-screen">
        <div className="loading-spinner"></div>
        <p>Loading...</p>
      </div>
    );
  }

  // Dashboard view for authenticated users
  if (isAuthenticated) {
    return <Dashboard />;
  }

  // Landing page for non-authenticated users
  return (
    <div className="App">
      <SEOHead />
      {/* Header with theme toggle and language selector */}
      <header className="header">
        <div className="container">
          <div className="header-content">
            {/* Sol taraf: Logo */}
            <div className="header-left">
              <div className="logo">
                <h2 className="text-gradient">NovaYield</h2>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="navigation desktop-nav">
              <a href="#home" className={`nav-link ${activeSection === 'home' ? 'active' : ''}`} onClick={(e) => handleNavClick(e, 'home')}>{t('nav.home')}</a>
              <a href="#comparison" className={`nav-link ${activeSection === 'comparison' ? 'active' : ''}`} onClick={(e) => handleNavClick(e, 'comparison')}>{t('nav.comparison')}</a>
              <a href="#plans" className={`nav-link ${activeSection === 'plans' ? 'active' : ''}`} onClick={(e) => handleNavClick(e, 'plans')}>{t('nav.plans')}</a>
              <a href="#calculator" className={`nav-link ${activeSection === 'calculator' ? 'active' : ''}`} onClick={(e) => handleNavClick(e, 'calculator')}>{t('nav.calculator')}</a>
              <a href="#trust" className={`nav-link ${activeSection === 'trust' ? 'active' : ''}`} onClick={(e) => handleNavClick(e, 'trust')}>{t('nav.trust')}</a>
              <a href="#howto" className={`nav-link ${activeSection === 'howto' ? 'active' : ''}`} onClick={(e) => handleNavClick(e, 'howto')}>{t('nav.howto')}</a>
            </nav>

            {/* Orta: Language Selector (mobilde) */}
            <div className="language-selector">
              <button
                onClick={toggleLanguageDropdown}
                className="language-dropdown-trigger"
                title={currentLanguage.name}
              >
                <img src={currentLanguage.flag} alt={currentLanguage.code.toUpperCase()} className="flag-image" />
                <ChevronDown
                  size={16}
                  className={`dropdown-arrow ${isLanguageDropdownOpen ? 'open' : ''}`}
                />
              </button>

              {isLanguageDropdownOpen && (
                <div className="language-dropdown">
                  {languages.map((language) => (
                    <button
                      key={language.code}
                      onClick={() => changeLanguage(language.code)}
                      className={`language-option ${i18n.language === language.code ? 'active' : ''}`}
                    >
                      <img src={language.flag} alt={language.code.toUpperCase()} className="flag-image" />
                      <span className="language-name">{language.name}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Sağ taraf: Language (desktop) + Theme + Mobile Menu */}
            <div className="header-controls">
              <div className="language-selector">
                <button
                  onClick={toggleLanguageDropdown}
                  className="language-dropdown-trigger"
                  title={currentLanguage.name}
                >
                  <img src={currentLanguage.flag} alt={currentLanguage.code.toUpperCase()} className="flag-image" />
                  <ChevronDown
                    size={16}
                    className={`dropdown-arrow ${isLanguageDropdownOpen ? 'open' : ''}`}
                  />
                </button>

                {isLanguageDropdownOpen && (
                  <div className="language-dropdown">
                    {languages.map((language) => (
                      <button
                        key={language.code}
                        onClick={() => changeLanguage(language.code)}
                        className={`language-option ${i18n.language === language.code ? 'active' : ''}`}
                      >
                        <img src={language.flag} alt={language.code.toUpperCase()} className="flag-image" />
                        <span className="language-name">{language.name}</span>
                      </button>
                    ))}
                  </div>
                )}
              </div>
              <button
                onClick={toggleTheme}
                className="theme-toggle"
                aria-label="Toggle theme"
              >
                {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}
              </button>
              <button
                onClick={toggleMobileMenu}
                className="mobile-menu-toggle"
                aria-label="Toggle mobile menu"
              >
                {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMobileMenuOpen && (
            <nav className="mobile-navigation">
              <a href="#home" className={`mobile-nav-link ${activeSection === 'home' ? 'active' : ''}`} onClick={(e) => handleNavClick(e, 'home')}>{t('nav.home')}</a>
              <a href="#comparison" className={`mobile-nav-link ${activeSection === 'comparison' ? 'active' : ''}`} onClick={(e) => handleNavClick(e, 'comparison')}>{t('nav.comparison')}</a>
              <a href="#plans" className={`mobile-nav-link ${activeSection === 'plans' ? 'active' : ''}`} onClick={(e) => handleNavClick(e, 'plans')}>{t('nav.plans')}</a>
              <a href="#calculator" className={`mobile-nav-link ${activeSection === 'calculator' ? 'active' : ''}`} onClick={(e) => handleNavClick(e, 'calculator')}>{t('nav.calculator')}</a>
              <a href="#trust" className={`mobile-nav-link ${activeSection === 'trust' ? 'active' : ''}`} onClick={(e) => handleNavClick(e, 'trust')}>{t('nav.trust')}</a>
              <a href="#howto" className={`mobile-nav-link ${activeSection === 'howto' ? 'active' : ''}`} onClick={(e) => handleNavClick(e, 'howto')}>{t('nav.howto')}</a>
            </nav>
          )}
        </div>
      </header>

      {/* Main content */}
      <main>
        <HeroSection />
        <ComparisonSection />
        <PlansSection />
        <APRCalculator />
        <TrustSection />
        <HowToSection />
      </main>

      {/* Footer */}
      <Footer />

      {/* Sticky contact button */}
      <button 
        className="sticky-contact-btn"
        onClick={() => setIsContactModalOpen(true)}
        aria-label="Contact us"
      >
        <MessageCircle size={24} />
      </button>

      {/* Contact modal */}
      <ContactModal
        isOpen={isContactModalOpen}
        onClose={() => setIsContactModalOpen(false)}
      />

      {/* Toast notifications */}
      <ToastContainer />
    </div>
  );
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
