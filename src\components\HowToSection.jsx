import React from 'react';
import { useTranslation } from 'react-i18next';
import { UserPlus, Send, TrendingUp } from 'lucide-react';
import AnimatedBackground from './AnimatedBackground';
import './HowToSection.css';

const HowToSection = () => {
  const { t } = useTranslation();

  const handleGetStarted = () => {
    // Hero section'daki kayıt alanına yönlendir
    const heroSection = document.getElementById('home');
    if (heroSection) {
      const headerHeight = 80; // Header yüksekliği
      const targetPosition = heroSection.offsetTop - headerHeight;

      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });

      // AuthCard'ı register moduna geçir ve highlight efekti ekle
      setTimeout(() => {
        const authCard = document.querySelector('.auth-card');
        if (authCard) {
          // AuthCard'a focus ver ve hafif bir highlight efekti ekle
          authCard.style.boxShadow = 'var(--shadow-xl), 0 0 60px rgba(99, 102, 241, 0.3)';
          setTimeout(() => {
            authCard.style.boxShadow = 'var(--shadow-xl), 0 0 40px rgba(99, 102, 241, 0.1)';
          }, 2000);
        }
      }, 800);
    }
  };

  const steps = [
    {
      number: '01',
      icon: <UserPlus className="step-icon" />,
      title: t('howto.step1.title'),
      description: t('howto.step1.description')
    },
    {
      number: '02',
      icon: <Send className="step-icon" />,
      title: t('howto.step2.title'),
      description: t('howto.step2.description')
    },
    {
      number: '03',
      icon: <TrendingUp className="step-icon" />,
      title: t('howto.step3.title'),
      description: t('howto.step3.description')
    }
  ];

  return (
    <section id="howto" className="howto-section section">
      <AnimatedBackground variant="geometric" />
      <div className="container">
        <div className="section-header text-center">
          <h2 className="section-title text-shimmer fade-in-up">
            {t('howto.title')}
          </h2>
          <p className="section-subtitle fade-in-up">
            {t('howto.subtitle')}
          </p>
        </div>

        <div className="steps-container">
          {steps.map((step, index) => (
            <div 
              key={index}
              className="step-card fade-in-up"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <div className="step-number">
                {step.number}
              </div>
              
              <div className="step-content">
                <div className="step-header">
                  {step.icon}
                  <h3 className="step-title" dangerouslySetInnerHTML={{ __html: step.title }}></h3>
                </div>

                <p className="step-description" dangerouslySetInnerHTML={{ __html: step.description }}>
                </p>
              </div>

              {index < steps.length - 1 && (
                <div className="step-connector">
                  <div className="connector-line"></div>
                  <div className="connector-arrow">→</div>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="howto-cta text-center fade-in-up">
          <button className="btn btn-primary btn-lg" onClick={handleGetStarted}>
            {t('howto.cta')}
            <TrendingUp size={20} />
          </button>
          <p className="cta-note">
            {t('howto.note')}
          </p>
        </div>
      </div>
    </section>
  );
};

export default HowToSection;
