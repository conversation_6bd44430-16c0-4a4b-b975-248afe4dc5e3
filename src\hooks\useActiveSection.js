import { useState, useEffect } from 'react';

export const useActiveSection = () => {
  const [activeSection, setActiveSection] = useState('home');

  useEffect(() => {
    const handleScroll = () => {
      const sections = ['home', 'comparison', 'plans', 'calculator', 'trust', 'howto'];
      const scrollPosition = window.scrollY;

      // Mobile ve desktop için farklı threshold değerleri (navbar offset'le<PERSON>yle uyu<PERSON>lu)
      const isMobile = window.innerWidth <= 768;
      const thresholds = {
        'home': 0,
        'comparison': isMobile ? 80 : 100,
        'plans': isMobile ? 80 : 100,
        'calculator': isMobile ? 120 : 200,
        'trust': isMobile ? 150 : 250,
        'howto': isMobile ? 160 : 280
      };

      for (let i = sections.length - 1; i >= 0; i--) {
        const section = document.getElementById(sections[i]);
        if (section) {
          const sectionTop = section.offsetTop;
          const threshold = thresholds[sections[i]] || 100;

          // Scroll pozisyonu section'ın başlangıcından threshold kadar önce olduğunda aktif yap
          if (scrollPosition >= sectionTop - threshold - 50) {
            setActiveSection(sections[i]);
            break;
          }
        }
      }
    };

    // İlk yüklemede kontrol et
    handleScroll();

    // Scroll event listener ekle
    window.addEventListener('scroll', handleScroll);
    
    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return activeSection;
};
