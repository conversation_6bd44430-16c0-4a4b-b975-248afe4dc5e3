.earnings-timeline {
  width: 100%;
  padding: 1rem 0;
  position: relative;
  margin-top: -1rem;
}

.timeline-container {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem 0;
}

/* Timeline line */
.timeline-line {
  position: absolute;
  top: 50%;
  left: 5%;
  right: 10%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  transform: translateY(-50%);
  z-index: 1;
}

.timeline-progress {
  height: 100%;
  background: linear-gradient(90deg,
    var(--accent-cyan) 0%,
    var(--accent-primary) 50%,
    var(--accent-pink) 100%
  );
  border-radius: 2px;
  transition: width 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: absolute;
  top: 0;
  box-shadow: 0 0 15px rgba(64, 224, 208, 0.4);
  animation: progress-glow 2s ease-in-out infinite alternate;
}

.timeline-progress::after {
  content: '';
  position: absolute;
  right: -6px;
  top: 50%;
  width: 12px;
  height: 12px;
  background: var(--accent-primary);
  border-radius: 50%;
  transform: translateY(-50%);
  box-shadow: 0 0 20px var(--accent-primary);
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Timeline start point (0 point) */
.timeline-start-point {
  position: absolute;
  left: 5%;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: var(--accent-primary);
  border-radius: 50%;
  z-index: 3;
  opacity: 1;
  box-shadow: 0 0 15px var(--accent-primary);
}

/* Timeline milestones */
.timeline-milestones {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 2;
  padding: 0 5% 0 10%;
}

.timeline-milestone {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.timeline-milestone:hover {
  transform: translateY(-5px);
}

/* Milestone point */
.milestone-point {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: 1rem;
}

.milestone-inner {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
}

.timeline-milestone.active .milestone-point {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  box-shadow: 0 0 20px rgba(123, 31, 162, 0.5);
}

.timeline-milestone.active .milestone-inner {
  background: white;
}

.timeline-milestone.selected .milestone-point {
  background: var(--accent-pink);
  border-color: var(--accent-pink);
  box-shadow: 0 0 30px rgba(233, 30, 99, 0.6);
  transform: scale(1.2);
}

.milestone-pulse {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--accent-primary);
  animation: pulse-ring 2s ease-out infinite;
}

/* Milestone content */
.milestone-content {
  text-align: center;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.milestone-year {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.timeline-milestone.active .milestone-year {
  color: var(--text-primary);
}

.timeline-milestone.selected .milestone-year {
  color: var(--accent-primary);
  font-weight: 700;
}

.milestone-details {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.8rem;
  backdrop-filter: blur(10px);
  animation: fadeInUp 0.5s ease-out;
  min-width: 130px;
  max-width: 150px;
  box-sizing: border-box;
  overflow: hidden;
}

.milestone-earnings {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--accent-green);
  margin-bottom: 0.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  word-break: break-word;
}

.milestone-usdt-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.milestone-percentage {
  font-size: 1rem;
  color: var(--accent-orange);
  font-weight: 600;
  margin-bottom: 0.3rem;
}

.milestone-total {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.2rem;
}

.milestone-total-label {
  font-size: 0.85rem;
  color: var(--accent-primary);
  font-weight: 700;
  letter-spacing: 0.5px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-tertiary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.milestone-total-amount {
  font-size: 0.85rem;
  color: var(--accent-primary);
  font-weight: 700;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-tertiary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 10px rgba(99, 102, 241, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  text-decoration: underline;
  text-decoration-color: var(--accent-primary);
  text-decoration-thickness: 2px;
  text-underline-offset: 2px;
}

.milestone-total-icon {
  width: 14px;
  height: 14px;
  object-fit: contain;
  flex-shrink: 0;
}

/* Timeline controls */
.timeline-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.timeline-control-btn {
  padding: 0.5rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-secondary);
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.timeline-control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--accent-primary);
  color: var(--text-primary);
}

.timeline-control-btn.active {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: white;
  box-shadow: 0 4px 15px rgba(123, 31, 162, 0.3);
}

/* Timeline summary */
.timeline-summary {
  margin-top: 3rem;
  display: flex;
  justify-content: center;
}

.summary-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  max-width: 400px;
  width: 100%;
}

.summary-header h4 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1.1rem;
  text-align: center;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.summary-item.highlight {
  background: rgba(123, 31, 162, 0.1);
  border-radius: 8px;
  padding: 0.8rem;
  border: 1px solid rgba(123, 31, 162, 0.2);
}

.summary-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.summary-value {
  color: var(--text-primary);
  font-weight: 600;
}

.summary-value.earnings {
  color: var(--accent-green);
  font-weight: 700;
}

/* Animations */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px var(--accent-primary);
    opacity: 1;
  }
  50% {
    box-shadow: 0 0 30px var(--accent-primary), 0 0 40px var(--accent-primary);
    opacity: 0.8;
  }
}

@keyframes progress-glow {
  0% {
    box-shadow: 0 0 15px rgba(64, 224, 208, 0.4);
  }
  100% {
    box-shadow: 0 0 25px rgba(64, 224, 208, 0.8), 0 0 35px rgba(64, 224, 208, 0.4);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Light mode specific styles - Sadece yıl butonları için */
[data-theme="light"] .timeline-control-btn {
  background: rgba(99, 102, 241, 0.06);
  border: 1.5px solid var(--accent-primary);
  color: var(--text-primary);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.12);
}

[data-theme="light"] .timeline-control-btn:hover {
  background: rgba(99, 102, 241, 0.12);
  border: 2px solid var(--accent-primary);
  color: var(--text-primary);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.25);
  transform: translateY(-2px);
}

[data-theme="light"] .timeline-control-btn.active {
  background: var(--accent-primary);
  border: 2px solid var(--accent-primary);
  color: white;
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

/* Light mode milestone noktaları için */
[data-theme="light"] .milestone-point {
  background: rgba(99, 102, 241, 0.1);
  border: 2px solid var(--accent-primary);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
}

[data-theme="light"] .milestone-inner {
  background: var(--accent-primary);
}

[data-theme="light"] .timeline-milestone.active .milestone-point {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
}

[data-theme="light"] .timeline-milestone.active .milestone-inner {
  background: white;
}

[data-theme="light"] .timeline-milestone.selected .milestone-point {
  background: var(--accent-pink);
  border-color: var(--accent-pink);
  box-shadow: 0 6px 20px rgba(236, 72, 153, 0.4);
  transform: scale(1.2);
}

/* Responsive design */
@media (max-width: 768px) {
  .earnings-timeline {
    overflow-x: hidden;
    width: 100%;
  }

  .timeline-container {
    max-width: 100%;
    padding: 1rem 0.25rem;
    overflow-x: hidden;
  }

  .timeline-milestones {
    padding: 0 1% 0 3%;
    gap: 0.25rem;
    flex-wrap: nowrap;
    justify-content: space-between;
    width: 100%;
  }

  .timeline-milestone {
    flex: 1;
    min-width: 0;
    max-width: calc(25% - 0.25rem);
  }

  .milestone-details {
    min-width: 70px;
    max-width: 85px;
    padding: 0.4rem 0.25rem;
    font-size: 0.7rem;
    margin: 0 auto;
  }

  .milestone-earnings {
    font-size: 0.75rem;
    word-break: break-all;
    line-height: 1.2;
  }

  .milestone-total {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.15rem;
  }

  .milestone-total-label {
    font-size: 0.65rem;
    color: var(--accent-primary);
    font-weight: 700;
    letter-spacing: 0.3px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-tertiary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .milestone-total-amount {
    font-size: 0.65rem;
    color: var(--accent-primary);
    font-weight: 700;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-tertiary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    text-decoration: underline;
    text-decoration-color: var(--accent-primary);
    text-decoration-thickness: 1.5px;
    text-underline-offset: 1px;
    word-break: break-all;
    line-height: 1.1;
  }

  .milestone-total-icon {
    width: 12px;
    height: 12px;
  }

  .milestone-percentage {
    font-size: 0.7rem;
  }

  .milestone-year {
    font-size: 0.8rem;
    text-align: center;
  }

  .milestone-content {
    min-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  .timeline-controls {
    flex-wrap: wrap;
    gap: 0.3rem;
    justify-content: center;
    padding: 0 0.5rem;
    margin-top: 1rem;
  }

  .timeline-control-btn {
    padding: 0.5rem 0.875rem;
    font-size: 0.8rem;
    min-width: auto;
    flex: 0 0 auto;
  }

  .timeline-line {
    left: 2%;
    right: 2%;
  }

  .timeline-start-point {
    left: 2%;
  }

  .milestone-usdt-icon {
    width: 12px;
    height: 12px;
  }
}

@media (max-width: 480px) {
  .timeline-container {
    padding: 0.75rem 0.125rem;
  }

  .timeline-milestones {
    padding: 0 0.5% 0 2%;
    gap: 0.125rem;
  }

  .timeline-milestone {
    max-width: calc(25% - 0.125rem);
  }

  .milestone-details {
    min-width: 60px;
    max-width: 75px;
    padding: 0.3rem 0.2rem;
    font-size: 0.65rem;
  }

  .milestone-earnings {
    font-size: 0.7rem;
    line-height: 1.1;
  }

  .milestone-total {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.1rem;
  }

  .milestone-total-label {
    font-size: 0.6rem;
    color: var(--accent-primary);
    font-weight: 700;
    letter-spacing: 0.2px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-tertiary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .milestone-total-amount {
    font-size: 0.6rem;
    color: var(--accent-primary);
    font-weight: 700;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-tertiary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.2rem;
    text-decoration: underline;
    text-decoration-color: var(--accent-primary);
    text-decoration-thickness: 1px;
    text-underline-offset: 1px;
    word-break: break-all;
    line-height: 1;
  }

  .milestone-total-icon {
    width: 10px;
    height: 10px;
  }

  .milestone-percentage {
    font-size: 0.65rem;
  }

  .milestone-year {
    font-size: 0.75rem;
  }

  .milestone-content {
    min-height: 90px;
  }

  .timeline-control-btn {
    padding: 0.4rem 0.7rem;
    font-size: 0.75rem;
  }

  .milestone-point {
    width: 16px;
    height: 16px;
  }

  .milestone-inner {
    width: 6px;
    height: 6px;
  }

  .milestone-usdt-icon {
    width: 10px;
    height: 10px;
  }

  .timeline-line {
    left: 1.5%;
    right: 1.5%;
  }

  .timeline-start-point {
    left: 1.5%;
  }
}
