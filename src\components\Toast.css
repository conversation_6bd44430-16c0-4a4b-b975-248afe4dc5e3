.toast-container {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-width: 400px;
}

.toast {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  transform: translateX(100%);
  opacity: 0;
}

.toast-visible {
  transform: translateX(0);
  opacity: 1;
}

.toast-hidden {
  transform: translateX(100%);
  opacity: 0;
}

.toast-success {
  border-left: 4px solid var(--accent-green);
}

.toast-error {
  border-left: 4px solid #ef4444;
}

.toast-warning {
  border-left: 4px solid var(--accent-gold);
}

.toast-info {
  border-left: 4px solid var(--accent-primary);
}

.toast-icon {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.toast-success .toast-icon {
  color: var(--accent-green);
}

.toast-error .toast-icon {
  color: #ef4444;
}

.toast-warning .toast-icon {
  color: var(--accent-gold);
}

.toast-info .toast-icon {
  color: var(--accent-primary);
}

.toast-message {
  flex: 1;
  color: var(--text-primary);
  font-size: 0.875rem;
  line-height: 1.4;
}

.toast-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.toast-close:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .toast-container {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
  }
  
  .toast {
    padding: 0.875rem;
  }
  
  .toast-message {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .toast-container {
    top: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
  }
  
  .toast {
    padding: 0.75rem;
    gap: 0.5rem;
  }
}
