import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

const SEOHead = () => {
  const { t, i18n } = useTranslation();

  useEffect(() => {
    // Update document title
    document.title = 'NovaYield - USDT ile Yüksek Getiri | Dolar Bazında Yatırım Platformu';
    
    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', 
        'USDT ile yıllık %102\'ye varan getiri elde edin. Dolar bazında güvenli yatırım platformu. Enflasyon ve kur riskinden korunun.'
      );
    }

    // Update language attribute
    document.documentElement.lang = i18n.language;

    // Add Open Graph meta tags
    const addMetaTag = (property, content) => {
      let meta = document.querySelector(`meta[property="${property}"]`);
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute('property', property);
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    };

    // Open Graph tags
    addMetaTag('og:title', 'NovaYield - USDT ile Yüksek Getiri');
    addMetaTag('og:description', 'USDT ile yıllık %102\'ye varan getiri elde edin. Dolar bazında güvenli yatırım platformu.');
    addMetaTag('og:type', 'website');
    addMetaTag('og:url', window.location.href);
    addMetaTag('og:site_name', 'NovaYield');
    addMetaTag('og:locale', i18n.language === 'tr' ? 'tr_TR' : 'en_US');

    // Twitter Card tags
    const addTwitterTag = (name, content) => {
      let meta = document.querySelector(`meta[name="${name}"]`);
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute('name', name);
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    };

    addTwitterTag('twitter:card', 'summary_large_image');
    addTwitterTag('twitter:title', 'NovaYield - USDT ile Yüksek Getiri');
    addTwitterTag('twitter:description', 'USDT ile yıllık %102\'ye varan getiri elde edin.');

    // Add structured data
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "FinancialService",
      "name": "NovaYield",
      "description": "USDT ile yüksek getiri sağlayan dolar bazında yatırım platformu",
      "url": window.location.href,
      "sameAs": [
        "https://instagram.com/novayield",
        "https://twitter.com/novayield"
      ],
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+90-540-543-33-22",
        "contactType": "customer service",
        "availableLanguage": ["Turkish", "English"]
      }
    };

    let scriptTag = document.querySelector('script[type="application/ld+json"]');
    if (!scriptTag) {
      scriptTag = document.createElement('script');
      scriptTag.type = 'application/ld+json';
      document.head.appendChild(scriptTag);
    }
    scriptTag.textContent = JSON.stringify(structuredData);

  }, [i18n.language]);

  return null; // This component doesn't render anything
};

export default SEOHead;
