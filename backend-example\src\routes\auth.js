import express from 'express';
import rateLimit from 'express-rate-limit';
import {
  register,
  login,
  verifyEmail,
  forgotPassword,
  resetPassword,
  verifyToken
} from '../controllers/authController.js';
import {
  validateRegister,
  validateLogin,
  validateForgotPassword,
  validateResetPassword,
  validateEmailVerification
} from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 dakika
  max: 5, // 5 deneme
  message: {
    success: false,
    message: 'Çok fazla giriş denemesi. 15 dakika sonra tekrar deneyin.'
  },
  standardHeaders: true,
  legacyHeaders: false
});

const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 saat
  max: 3, // 3 kayıt
  message: {
    success: false,
    message: 'Çok fazla kayıt denemesi. 1 saat sonra tekrar deneyin.'
  }
});

const forgotPasswordLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 saat
  max: 3, // 3 şifre sıfırlama
  message: {
    success: false,
    message: 'Çok fazla şifre sıfırlama isteği. 1 saat sonra tekrar deneyin.'
  }
});

// Public routes
router.post('/register', registerLimiter, validateRegister, register);
router.post('/login', authLimiter, validateLogin, login);
router.get('/verify/:token', validateEmailVerification, verifyEmail);
router.post('/forgot-password', forgotPasswordLimiter, validateForgotPassword, forgotPassword);
router.post('/reset-password/:token', validateResetPassword, resetPassword);

// Protected routes
router.get('/verify-token', authenticateToken, verifyToken);

// Resend verification email
router.post('/resend-verification', authLimiter, async (req, res) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email adresi gereklidir'
      });
    }

    const user = await User.findByEmail(email);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Kullanıcı bulunamadı'
      });
    }

    if (user.isVerified) {
      return res.status(400).json({
        success: false,
        message: 'Email adresi zaten doğrulanmış'
      });
    }

    // Yeni doğrulama token'ı oluştur
    const verificationToken = user.createVerificationToken();
    await user.save();

    // Email gönder
    await sendVerificationEmail(email, verificationToken, user.firstName);

    res.status(200).json({
      success: true,
      message: 'Doğrulama emaili tekrar gönderildi'
    });

  } catch (error) {
    console.error('Resend verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
});

// Logout (client-side token silme için endpoint)
router.post('/logout', authenticateToken, (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Başarıyla çıkış yapıldı'
  });
});

// Check email availability
router.post('/check-email', async (req, res) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email adresi gereklidir'
      });
    }

    const existingUser = await User.findByEmail(email);
    
    res.status(200).json({
      success: true,
      available: !existingUser,
      message: existingUser ? 'Email adresi kullanılıyor' : 'Email adresi kullanılabilir'
    });

  } catch (error) {
    console.error('Check email error:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
});

export default router;
