{"name": "novayield-backend", "version": "1.0.0", "description": "NovaYield Investment Platform Backend API", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "seed": "node src/scripts/seedDatabase.js", "setup": "npm install && npm run seed"}, "keywords": ["nodejs", "express", "mongodb", "authentication", "investment", "crypto", "api"], "author": "NovaYield Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "nodemailer": "^6.9.7", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}