import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Instagram, Twitter, Mail, Phone } from 'lucide-react';
import './Footer.css';

const Footer = () => {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);

  const handleNewsletterSubmit = async (e) => {
    e.preventDefault();
    if (!email) return;

    setIsSubscribing(true);

    // Simulate API call
    setTimeout(() => {
      alert('Newsletter aboneliği başarılı!');
      setEmail('');
      setIsSubscribing(false);
    }, 1000);
  };

  const footerLinks = [
    { label: t('footer.about'), href: '#about' },
    { label: t('footer.faq'), href: '#faq' },
    { label: t('footer.terms'), href: '#terms' },
    { label: t('footer.privacy'), href: '#privacy' }
  ];

  const socialLinks = [
    {
      icon: <Instagram size={20} />,
      href: 'https://instagram.com/novayield',
      label: 'Instagram'
    },
    {
      icon: <Twitter size={20} />,
      href: 'https://twitter.com/novayield',
      label: 'Twitter'
    },
    {
      icon: <Mail size={20} />,
      href: 'mailto:<EMAIL>',
      label: 'Email'
    },
    {
      icon: <Phone size={20} />,
      href: 'https://wa.me/905405433322',
      label: 'WhatsApp'
    }
  ];

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          <div className="footer-brand">
            <h3 className="footer-logo text-gradient">NovaYield</h3>
            <p className="footer-tagline" dangerouslySetInnerHTML={{ __html: t('footer.description') }}>
            </p>
            <div className="social-links">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-link"
                  aria-label={social.label}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          <div className="footer-links">
            <h4>{t('footer.quick_links')}</h4>
            <ul>
              {footerLinks.map((link, index) => (
                <li key={index}>
                  <a href={link.href} className="footer-link">
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div className="footer-contact">
            <h4>{t('footer.contact_info')}</h4>
            <div className="contact-info">
              <p>
                <Mail size={16} />
                <span><EMAIL></span>
              </p>
              <p>
                <Phone size={16} />
                <span>+90 540 543 33 22</span>
              </p>
            </div>
          </div>

          <div className="footer-newsletter">
            <h4>{t('footer.updates')}</h4>
            <p>{t('footer.newsletter_desc')}</p>
            <form className="newsletter-form" onSubmit={handleNewsletterSubmit}>
              <input
                type="email"
                placeholder={t('footer.email_placeholder')}
                className="newsletter-input"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
              <button
                type="submit"
                className="newsletter-btn"
                disabled={isSubscribing}
              >
                {isSubscribing ? t('footer.subscribing') : t('footer.subscribe')}
              </button>
            </form>
          </div>
        </div>

        <div className="footer-bottom">
          <div className="footer-divider"></div>
          <div className="footer-bottom-content">
            <p className="copyright">
              {t('footer.copyright')}
            </p>
            <div className="footer-badges">
              <span className="badge">{t('footer.ssl')}</span>
              <span className="badge" dangerouslySetInnerHTML={{ __html: t('footer.usdt') }}></span>
              <span className="badge">{t('footer.support')}</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
