import React from 'react';
import { useTranslation } from 'react-i18next';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import AnimatedBackground from './AnimatedBackground';
import './ComparisonSection.css';

const ComparisonSection = () => {
  const { t } = useTranslation();

  const comparisonData = [
    {
      platform: t('comparison.banks_fx'),
      yield: t('comparison.yield_banks_fx'),
      icon: <Minus className="yield-icon neutral" />,
      highlight: false
    },
    {
      platform: t('comparison.binance'),
      yield: t('comparison.yield_binance'),
      icon: <TrendingUp className="yield-icon low" />,
      highlight: false,
      logo: '/icons/binance.png'
    },
    {
      platform: t('comparison.banks_tl'),
      yield: t('comparison.yield_banks_tl'),
      icon: <TrendingUp className="yield-icon medium" />,
      highlight: false
    },
    {
      platform: t('comparison.novayield'),
      yield: t('comparison.yield_novayield'),
      icon: <TrendingUp className="yield-icon high" />,
      highlight: true
    }
  ];

  return (
    <section id="comparison" className="comparison-section section">
      <AnimatedBackground variant="geometric" />
      <div className="container">
        <div className="section-header text-center">
          <h2 className="section-title text-shimmer fade-in-up">
            {t('comparison.title')}
          </h2>
          <p className="section-subtitle fade-in-up">
            {t('comparison.subtitle')}
          </p>
        </div>

        <div className="comparison-table fade-in-up">
          <div className="table-header">
            <div className="header-cell">
              {t('comparison.platform')}
            </div>
            <div className="header-cell">
              {t('comparison.yield')}
            </div>
          </div>

          <div className="table-body">
            {comparisonData.map((item, index) => (
              <div 
                key={index} 
                className={`table-row ${item.highlight ? 'highlight' : ''}`}
              >
                <div className="platform-cell">
                  {item.logo && <img src={item.logo} alt={item.platform} className="platform-logo" />}
                  <span className="platform-name">{item.platform}</span>
                </div>
                <div className="yield-cell">
                  {item.icon}
                  <span className="yield-value">{item.yield}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="comparison-note">
          <p>
            {t('comparison.disclaimer')}
          </p>
        </div>
      </div>
    </section>
  );
};

export default ComparisonSection;
