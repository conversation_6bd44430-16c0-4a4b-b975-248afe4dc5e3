import mongoose from 'mongoose';

const investmentSchema = new mongoose.Schema({
  // Kullanıcı referansı
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Kullanıcı ID gereklidir']
  },
  
  // Yatırım detayları
  packageType: {
    type: String,
    required: [true, 'Paket türü gereklidir'],
    enum: ['starter', 'growth', 'premium', 'enterprise'],
    default: 'starter'
  },
  
  amount: {
    type: Number,
    required: [true, 'Yatırım tutarı gereklidir'],
    min: [100, 'Minimum yatırım tutarı 100 USDT'],
    max: [1000000, 'Maximum yatırım tutarı 1,000,000 USDT']
  },
  
  currency: {
    type: String,
    required: true,
    enum: ['USDT', 'USD', 'EUR'],
    default: 'USDT'
  },
  
  // Blockchain bilgileri
  network: {
    type: String,
    required: [true, '<PERSON>ğ seçimi gereklidir'],
    enum: ['TRC20', 'ERC20', 'BEP20'],
    default: 'TRC20'
  },
  
  walletAddress: {
    type: String,
    required: [true, 'Cüzdan adresi gereklidir'],
    trim: true
  },
  
  transactionHash: {
    type: String,
    trim: true,
    unique: true,
    sparse: true // Null değerler için unique constraint uygulanmaz
  },
  
  // Yatırım durumu
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'active', 'completed', 'cancelled', 'failed'],
    default: 'pending'
  },
  
  // APR ve getiri bilgileri
  aprRate: {
    type: Number,
    required: true,
    min: [0, 'APR oranı 0\'dan küçük olamaz'],
    max: [200, 'APR oranı 200\'den büyük olamaz']
  },
  
  lockPeriod: {
    type: Number, // Gün cinsinden
    required: true,
    min: [30, 'Minimum kilitlenme süresi 30 gün'],
    max: [365, 'Maximum kilitlenme süresi 365 gün']
  },
  
  // Tarihler
  startDate: {
    type: Date,
    default: null
  },
  
  endDate: {
    type: Date,
    default: null
  },
  
  maturityDate: {
    type: Date,
    default: null
  },
  
  // Getiri hesaplamaları
  expectedReturn: {
    type: Number,
    default: 0
  },
  
  currentReturn: {
    type: Number,
    default: 0
  },
  
  totalReturn: {
    type: Number,
    default: 0
  },
  
  // Ödeme bilgileri
  paymentDetails: {
    method: {
      type: String,
      enum: ['crypto', 'bank_transfer', 'card'],
      default: 'crypto'
    },
    
    reference: String,
    
    confirmationCount: {
      type: Number,
      default: 0
    },
    
    requiredConfirmations: {
      type: Number,
      default: 3
    }
  },
  
  // Notlar ve açıklamalar
  notes: {
    type: String,
    maxlength: [500, 'Not 500 karakterden uzun olamaz']
  },
  
  adminNotes: {
    type: String,
    maxlength: [1000, 'Admin notu 1000 karakterden uzun olamaz']
  },
  
  // Metadata
  isAutoRenew: {
    type: Boolean,
    default: false
  },
  
  riskLevel: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  
  // Tracking
  ipAddress: String,
  userAgent: String,
  
  // Güncellemeler
  statusHistory: [{
    status: {
      type: String,
      enum: ['pending', 'confirmed', 'active', 'completed', 'cancelled', 'failed']
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    note: String,
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }]
  
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual fields
investmentSchema.virtual('isActive').get(function() {
  return this.status === 'active';
});

investmentSchema.virtual('isCompleted').get(function() {
  return this.status === 'completed';
});

investmentSchema.virtual('daysRemaining').get(function() {
  if (!this.endDate) return null;
  const now = new Date();
  const end = new Date(this.endDate);
  const diffTime = end - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
});

investmentSchema.virtual('progressPercentage').get(function() {
  if (!this.startDate || !this.endDate) return 0;
  
  const now = new Date();
  const start = new Date(this.startDate);
  const end = new Date(this.endDate);
  
  if (now < start) return 0;
  if (now > end) return 100;
  
  const totalDuration = end - start;
  const elapsed = now - start;
  
  return Math.round((elapsed / totalDuration) * 100);
});

// Indexes
investmentSchema.index({ user: 1, createdAt: -1 });
investmentSchema.index({ status: 1 });
investmentSchema.index({ network: 1 });
investmentSchema.index({ transactionHash: 1 });
investmentSchema.index({ endDate: 1 });
investmentSchema.index({ packageType: 1 });

// Pre-save middleware
investmentSchema.pre('save', function(next) {
  // Yatırım aktif hale geldiğinde tarihleri ayarla
  if (this.isModified('status') && this.status === 'active' && !this.startDate) {
    this.startDate = new Date();
    this.endDate = new Date(Date.now() + this.lockPeriod * 24 * 60 * 60 * 1000);
    this.maturityDate = this.endDate;
    
    // Beklenen getiriyi hesapla
    this.expectedReturn = this.amount * (this.aprRate / 100) * (this.lockPeriod / 365);
  }
  
  // Status history'ye ekle
  if (this.isModified('status')) {
    this.statusHistory.push({
      status: this.status,
      timestamp: new Date(),
      note: `Status changed to ${this.status}`
    });
  }
  
  next();
});

// Instance methods
investmentSchema.methods.calculateCurrentReturn = function() {
  if (!this.startDate || this.status !== 'active') return 0;
  
  const now = new Date();
  const start = new Date(this.startDate);
  const daysElapsed = Math.floor((now - start) / (1000 * 60 * 60 * 24));
  
  // Günlük getiri hesapla
  const dailyRate = this.aprRate / 365 / 100;
  const currentReturn = this.amount * dailyRate * daysElapsed;
  
  this.currentReturn = Math.round(currentReturn * 100) / 100;
  return this.currentReturn;
};

investmentSchema.methods.updateStatus = function(newStatus, note = '', updatedBy = null) {
  this.status = newStatus;
  this.statusHistory.push({
    status: newStatus,
    timestamp: new Date(),
    note,
    updatedBy
  });
};

// Static methods
investmentSchema.statics.findByUser = function(userId) {
  return this.find({ user: userId }).sort({ createdAt: -1 });
};

investmentSchema.statics.findActiveInvestments = function() {
  return this.find({ status: 'active' });
};

investmentSchema.statics.getTotalInvestedByUser = function(userId) {
  return this.aggregate([
    { $match: { user: mongoose.Types.ObjectId(userId), status: { $in: ['active', 'completed'] } } },
    { $group: { _id: null, total: { $sum: '$amount' } } }
  ]);
};

export default mongoose.model('Investment', investmentSchema);
