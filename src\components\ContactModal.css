.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 1rem;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-content {
  background-color: var(--bg-primary);
  border-radius: 1.5rem;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-color);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--text-primary);
}

.modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.modal-body {
  padding: 2rem;
}

.modal-description {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
  text-align: center;
}

.contact-options {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.contact-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  cursor: pointer;
}

.contact-option:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-primary);
}

.contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.contact-info {
  flex: 1;
}

.contact-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: var(--text-primary);
}

.contact-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.contact-arrow {
  color: var(--text-muted);
  font-size: 1.25rem;
  font-weight: 700;
  transition: all 0.3s ease;
}

.contact-option:hover .contact-arrow {
  color: var(--accent-primary);
  transform: translateX(4px);
}

.modal-footer {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.footer-note {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin: 0;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-backdrop {
    padding: 0.5rem;
  }
  
  .modal-content {
    border-radius: 1rem;
  }
  
  .modal-header {
    padding: 1.5rem 1.5rem 1rem;
  }
  
  .modal-title {
    font-size: 1.25rem;
  }
  
  .modal-close {
    width: 2.25rem;
    height: 2.25rem;
  }
  
  .modal-body {
    padding: 1.5rem;
  }
  
  .contact-option {
    padding: 1.25rem;
  }
  
  .contact-icon {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .contact-title {
    font-size: 1rem;
  }
  
  .contact-description {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .modal-backdrop {
    padding: 0;
    align-items: flex-end;
  }
  
  .modal-content {
    border-radius: 1rem 1rem 0 0;
    max-height: 80vh;
  }
  
  .modal-header {
    padding: 1.25rem 1.25rem 0.75rem;
  }
  
  .modal-title {
    font-size: 1.125rem;
  }
  
  .modal-close {
    width: 2rem;
    height: 2rem;
  }
  
  .modal-body {
    padding: 1.25rem;
  }
  
  .contact-option {
    padding: 1rem;
    gap: 0.75rem;
  }
  
  .contact-icon {
    width: 2.25rem;
    height: 2.25rem;
  }
  
  .contact-title {
    font-size: 0.95rem;
  }
  
  .contact-description {
    font-size: 0.75rem;
  }
  
  .contact-arrow {
    font-size: 1.125rem;
  }
}
