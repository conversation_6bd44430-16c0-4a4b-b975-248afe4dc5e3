.support-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 1rem;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.support-modal-content {
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1.5rem;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

/* Dark Mode Support Modal */
[data-theme="dark"] .support-modal-content {
  background:
    linear-gradient(135deg,
      rgba(60, 60, 60, 0.3) 0%,
      rgba(45, 45, 45, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Support Modal */
[data-theme="light"] .support-modal-content {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.support-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid var(--border-color);
}

.support-modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--text-primary);
}

.support-modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.support-modal-close:hover {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.support-modal-body {
  padding: 2rem;
}

.support-modal-description {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
  text-align: center;
}

.support-options {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.support-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Support Option */
[data-theme="dark"] .support-option {
  background:
    linear-gradient(135deg,
      rgba(60, 60, 60, 0.3) 0%,
      rgba(45, 45, 45, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Support Option */
[data-theme="light"] .support-option {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.support-option:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-primary);
}

.support-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.whatsapp-icon {
  background: linear-gradient(135deg, #25D366, #128C7E);
  color: white;
}

.telegram-icon {
  background: linear-gradient(135deg, #0088cc, #005577);
  color: white;
}

.live-chat-icon {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
}

.support-info {
  flex: 1;
}

.support-title {
  margin: 0 0 0.25rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.support-description {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.support-arrow {
  font-size: 1.25rem;
  color: var(--text-secondary);
  transition: transform 0.3s ease;
}

.support-option:hover .support-arrow {
  transform: translateX(4px);
}

.support-modal-footer {
  padding: 1.5rem 2rem 2rem;
  border-top: 1px solid var(--border-color);
  text-align: center;
}

.support-footer-badges {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.support-badge {
  padding: 0.25rem 0.75rem;
  background: var(--accent-primary);
  color: white;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.support-badge.ssl {
  background: var(--accent-green);
}

.support-badge.usdt {
  background: var(--accent-gold);
}

.support-badge.support-24-7 {
  background: var(--accent-primary);
}

.support-footer-text {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
  .support-modal-backdrop {
    padding: 0.5rem;
  }
  
  .support-modal-content {
    border-radius: 1rem;
  }
  
  .support-modal-header {
    padding: 1.5rem 1.5rem 1rem;
  }
  
  .support-modal-title {
    font-size: 1.25rem;
  }
  
  .support-modal-close {
    width: 2.25rem;
    height: 2.25rem;
  }
  
  .support-modal-body {
    padding: 1.5rem;
  }
  
  .support-option {
    padding: 1.25rem;
  }
  
  .support-icon {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .support-title {
    font-size: 1rem;
  }
  
  .support-description {
    font-size: 0.8rem;
  }

  .support-footer-badges {
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .support-modal-backdrop {
    padding: 0;
    align-items: flex-end;
  }
  
  .support-modal-content {
    border-radius: 1rem 1rem 0 0;
    max-height: 80vh;
  }
  
  .support-modal-header {
    padding: 1.25rem 1.25rem 0.75rem;
  }
  
  .support-modal-title {
    font-size: 1.125rem;
  }
  
  .support-modal-close {
    width: 2rem;
    height: 2rem;
  }
  
  .support-modal-body {
    padding: 1.25rem;
  }
  
  .support-option {
    padding: 1rem;
    gap: 0.75rem;
  }
  
  .support-icon {
    width: 2.25rem;
    height: 2.25rem;
  }
  
  .support-title {
    font-size: 0.95rem;
  }
  
  .support-description {
    font-size: 0.75rem;
  }
  
  .support-arrow {
    font-size: 1.125rem;
  }
}
