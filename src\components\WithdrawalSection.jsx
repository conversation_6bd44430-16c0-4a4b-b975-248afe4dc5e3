import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Wallet, 
  TrendingUp, 
  Clock, 
  CheckCircle,
  ArrowDownLeft,
  Eye,
  Calendar,
  ExternalLink,
  Copy,
  DollarSign
} from 'lucide-react';
import './WithdrawalSection.css';

const WithdrawalSection = ({ investment, showBalance }) => {
  const { t } = useTranslation();
  const [showWithdrawHistory, setShowWithdrawHistory] = useState(false);
  const [copied, setCopied] = useState('');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString(t('common.locale'), {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const copyToClipboard = async (text, type) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(type);
      setTimeout(() => setCopied(''), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const getNetworkColor = (network) => {
    const colors = {
      'TRC20': '#00d4aa',
      'ERC20': '#627eea',
      'BEP20': '#f3ba2f'
    };
    return colors[network] || '#6366f1';
  };

  const truncateAddress = (address) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-6)}`;
  };

  return (
    <div className="withdrawal-section">
      <div className="withdrawal-header">
        <h4>
          <Wallet size={20} />
          {t('withdrawal.title')}
        </h4>
        <span className="next-payment">
          <Calendar size={16} />
          {t('withdrawal.next_payment')}: {formatDate(investment.nextPaymentDate)}
        </span>
      </div>

      <div className="withdrawal-grid">
        {/* Birikmiş Bakiye */}
        <div className="withdrawal-card accumulating">
          <div className="card-header">
            <Clock size={20} />
            <span>{t('withdrawal.accumulated_balance')}</span>
          </div>
          <div className="card-amount">
            {showBalance ? formatCurrency(investment.accumulatedBalance) : '••••••'}
          </div>
          <div className="card-description">
            {t('withdrawal.accumulated_desc')}
          </div>
        </div>

        {/* Çekime Hazır */}
        <div className="withdrawal-card available">
          <div className="card-header">
            <TrendingUp size={20} />
            <span>{t('withdrawal.available_withdraw')}</span>
          </div>
          <div className="card-amount">
            {showBalance ? formatCurrency(investment.availableWithdraw) : '••••••'}
          </div>
          <div className="card-description">
            {t('withdrawal.available_desc')}
          </div>
          {investment.availableWithdraw > 0 && (
            <button className="withdraw-btn">
              <ArrowDownLeft size={16} />
              {t('withdrawal.withdraw_now')}
            </button>
          )}
        </div>

        {/* Önceden Çekilen */}
        <div className="withdrawal-card withdrawn">
          <div className="card-header">
            <CheckCircle size={20} />
            <span>{t('withdrawal.total_withdrawn')}</span>
          </div>
          <div className="card-amount">
            {showBalance ? formatCurrency(investment.totalWithdrawn) : '••••••'}
          </div>
          <div className="card-description">
            {t('withdrawal.withdrawn_desc')}
          </div>
          {investment.withdrawHistory?.length > 0 ? (
            <button
              className="history-btn"
              onClick={() => setShowWithdrawHistory(!showWithdrawHistory)}
            >
              <Eye size={16} />
              {t('withdrawal.view_history')}
            </button>
          ) : (
            <div className="no-history">
              {t('withdrawal.no_history')}
            </div>
          )}
        </div>
      </div>

      {/* Aylık Ödeme Bilgisi */}
      <div className="monthly-payment-info">
        <div className="payment-amount">
          <DollarSign size={20} />
          <div>
            <span className="payment-label">{t('withdrawal.monthly_payment')}</span>
            <span className="payment-value">
              {showBalance ? formatCurrency(investment.monthlyPayment) : '••••••'}
            </span>
          </div>
        </div>
        <div className="payment-schedule">
          <span className="schedule-label">{t('withdrawal.payment_schedule')}</span>
          <span className="schedule-value">{t('withdrawal.monthly_on_17th')}</span>
        </div>
      </div>

      {/* Çekim Geçmişi Modal */}
      {showWithdrawHistory && (
        <div className="withdraw-history-modal">
          <div className="modal-overlay" onClick={() => setShowWithdrawHistory(false)}>
            <div className="history-content" onClick={(e) => e.stopPropagation()}>
              <div className="history-header">
                <h3>{t('withdrawal.history_title')}</h3>
                <button 
                  className="close-history"
                  onClick={() => setShowWithdrawHistory(false)}
                >
                  ×
                </button>
              </div>
              
              <div className="history-list">
                {investment.withdrawHistory.map((withdrawal) => (
                  <div key={withdrawal.id} className="history-item">
                    <div className="history-main">
                      <div className="history-amount">
                        {formatCurrency(withdrawal.amount)}
                      </div>
                      <div className="history-date">
                        {formatDate(withdrawal.date)}
                      </div>
                    </div>
                    
                    <div className="history-details">
                      <div className="detail-row">
                        <span className="detail-label">{t('withdrawal.currency')}:</span>
                        <span className="detail-value">{withdrawal.currency}</span>
                      </div>
                      
                      <div className="detail-row">
                        <span className="detail-label">{t('withdrawal.network')}:</span>
                        <span 
                          className="detail-value network-badge"
                          style={{ backgroundColor: getNetworkColor(withdrawal.network) }}
                        >
                          {withdrawal.network}
                        </span>
                      </div>
                      
                      <div className="detail-row">
                        <span className="detail-label">{t('withdrawal.wallet_address')}:</span>
                        <div className="address-container">
                          <span className="detail-value address">
                            {truncateAddress(withdrawal.walletAddress)}
                          </span>
                          <button 
                            className={`copy-address ${copied === `address-${withdrawal.id}` ? 'copied' : ''}`}
                            onClick={() => copyToClipboard(withdrawal.walletAddress, `address-${withdrawal.id}`)}
                          >
                            <Copy size={12} />
                          </button>
                        </div>
                      </div>
                      
                      {withdrawal.txHash && (
                        <div className="detail-row">
                          <span className="detail-label">{t('withdrawal.tx_hash')}:</span>
                          <div className="tx-container">
                            <span className="detail-value tx-hash">
                              {truncateAddress(withdrawal.txHash)}
                            </span>
                            <button 
                              className={`copy-tx ${copied === `tx-${withdrawal.id}` ? 'copied' : ''}`}
                              onClick={() => copyToClipboard(withdrawal.txHash, `tx-${withdrawal.id}`)}
                            >
                              <Copy size={12} />
                            </button>
                            <a 
                              href={`https://tronscan.org/#/transaction/${withdrawal.txHash}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="view-tx"
                            >
                              <ExternalLink size={12} />
                            </a>
                          </div>
                        </div>
                      )}
                      
                      <div className="detail-row">
                        <span className="detail-label">{t('withdrawal.status')}:</span>
                        <span className={`detail-value status ${withdrawal.status}`}>
                          {t(`withdrawal.status_${withdrawal.status}`)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WithdrawalSection;
