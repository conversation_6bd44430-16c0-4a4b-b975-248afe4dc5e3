.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

/* Financial Pattern */
.financial-pattern {
  position: relative;
  width: 100%;
  height: 100%;
}

.symbol {
  position: absolute;
  font-size: 2rem;
  font-weight: bold;
  opacity: 0.25;
  animation: floatSymbol 8s ease-in-out infinite;
}

[data-theme="dark"] .symbol {
  opacity: 0.35;
}

.symbol-dollar {
  top: 10%;
  left: 10%;
  color: var(--accent-gold);
  animation-delay: 0s;
}

.symbol-euro {
  top: 10%;
  left: 40%;
  color: var(--accent-primary);
  animation-delay: 1s;
}

.symbol-pound {
  bottom: 30%;
  left: 20%;
  color: var(--accent-green);
  animation-delay: 2s;
}

.symbol-yen {
  top: 60%;
  right: 25%;
  color: var(--accent-cyan);
  animation-delay: 3s;
}

.symbol-bitcoin {
  bottom: 20%;
  right: 10%;
  color: var(--accent-orange);
  animation-delay: 4s;
}

.symbol-usdt {
  top: 40%;
  left: 5%;
  color: var(--accent-green);
  animation-delay: 5s;
}

.symbol-turkish-lira {
  bottom: 20%;
  left: 15%;
  color: var(--accent-orange);
  animation-delay: 1.5s;
}



.symbol-yuan {
  bottom: 15%;
  left: 35%;
  color: var(--accent-pink);
  animation-delay: 2.5s;
}

.symbol-ruble {
  top: 70%;
  left: 15%;
  color: var(--accent-cyan);
  animation-delay: 3.5s;
}

.symbol-rupee {
  bottom: 40%;
  right: 40%;
  color: var(--accent-gold);
  animation-delay: 4.5s;
}

.symbol-won {
  top: 65%;
  right: 30%;
  color: var(--accent-primary);
  animation-delay: 6s;
}

.symbol-franc {
  top: 45%;
  left: 85%;
  color: var(--accent-green);
  animation-delay: 6.5s;
}

.symbol-ethereum {
  top: 80%;
  right: 50%;
  color: var(--accent-cyan);
  animation-delay: 7s;
}

@keyframes floatSymbol {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.25;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.4;
  }
}

[data-theme="dark"] .symbol {
  animation: floatSymbolDark 8s ease-in-out infinite;
}



@keyframes floatSymbolDark {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.35;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.6;
  }
}

.financial-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, var(--accent-primary)35, transparent);
  height: 1px;
  animation: moveLine 6s linear infinite;
}

[data-theme="dark"] .financial-line {
  background: linear-gradient(90deg, transparent, var(--accent-primary)50, transparent);
}

.line-1 {
  top: 25%;
  width: 100%;
  animation-delay: 0s;
}

.line-2 {
  top: 50%;
  width: 100%;
  animation-delay: 2s;
}

.line-3 {
  top: 75%;
  width: 100%;
  animation-delay: 4s;
}

@keyframes moveLine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.floating-dot {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  animation: floatDot 4s ease-in-out infinite;
}

.dot-1 {
  top: 15%;
  left: 30%;
  background: var(--accent-primary);
  animation-delay: 0s;
}

.dot-2 {
  top: 35%;
  right: 20%;
  background: var(--accent-gold);
  animation-delay: 1s;
}

.dot-3 {
  bottom: 25%;
  left: 40%;
  background: var(--accent-cyan);
  animation-delay: 2s;
}

.dot-4 {
  top: 70%;
  right: 35%;
  background: var(--accent-pink);
  animation-delay: 3s;
}

.dot-5 {
  bottom: 40%;
  right: 50%;
  background: var(--accent-green);
  animation-delay: 4s;
}

@keyframes floatDot {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-15px) scale(1.5);
    opacity: 1;
  }
}

[data-theme="dark"] .floating-dot {
  animation: floatDotDark 4s ease-in-out infinite;
}

@keyframes floatDotDark {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-15px) scale(1.5);
    opacity: 1;
  }
}

/* Geometric Pattern */
.geometric-pattern {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  opacity: 0.15;
}

[data-theme="dark"] .shape {
  opacity: 0.25;
}

.triangle-1 {
  top: 20%;
  left: 15%;
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 35px solid var(--accent-primary);
  animation: rotateShape 10s linear infinite;
}

.triangle-2 {
  bottom: 30%;
  right: 20%;
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-bottom: 25px solid var(--accent-cyan);
  animation: rotateShape 8s linear infinite reverse;
}

.circle-1 {
  top: 40%;
  right: 10%;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--accent-pink);
  animation: scaleShape 6s ease-in-out infinite;
}

.circle-2 {
  bottom: 20%;
  left: 25%;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: var(--accent-gold);
  animation: scaleShape 4s ease-in-out infinite;
  animation-delay: 2s;
}

.square-1 {
  top: 60%;
  left: 10%;
  width: 30px;
  height: 30px;
  background: var(--accent-green);
  animation: rotateShape 12s linear infinite;
  animation-delay: 1s;
}

.square-2 {
  top: 10%;
  right: 30%;
  width: 20px;
  height: 20px;
  background: var(--accent-orange);
  animation: rotateShape 7s linear infinite reverse;
  animation-delay: 3s;
}

@keyframes rotateShape {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes scaleShape {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
}

/* Particles Pattern */
.particles-pattern {
  position: relative;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  border-radius: 50%;
  animation: moveParticle 8s linear infinite;
  bottom: -10px;
}

/* Different sizes for variety */
.particle:nth-child(odd) {
  width: 3px;
  height: 3px;
  background: var(--accent-cyan);
}

.particle:nth-child(even) {
  width: 2px;
  height: 2px;
  background: var(--accent-primary);
}

.particle:nth-child(3n) {
  width: 4px;
  height: 4px;
  background: var(--accent-pink);
}

.particle:nth-child(5n) {
  width: 2px;
  height: 2px;
  background: var(--accent-gold);
}

.particle:nth-child(7n) {
  width: 5px;
  height: 5px;
  background: var(--accent-green);
}

/* Star particles with trails */
.particle:nth-child(11n) {
  width: 3px;
  height: 3px;
  background: var(--accent-gold);
  box-shadow: 0 0 8px var(--accent-gold), 0 0 16px var(--accent-gold);
  animation: moveStarParticle 6s linear infinite;
}

.particle:nth-child(13n) {
  width: 4px;
  height: 4px;
  background: var(--accent-cyan);
  box-shadow: 0 0 10px var(--accent-cyan), 0 0 20px var(--accent-cyan);
  animation: moveStarParticle 7s linear infinite;
}

/* Additional particle variations for continuous flow */
.particle:nth-child(17n) {
  width: 2px;
  height: 2px;
  background: var(--accent-orange);
  animation: moveDiagonalParticle 9s linear infinite;
}

.particle:nth-child(19n) {
  width: 3px;
  height: 3px;
  background: var(--accent-purple);
  animation: moveCurvedParticle 10s linear infinite;
}

.particle:nth-child(23n) {
  width: 1px;
  height: 1px;
  background: var(--accent-primary);
  box-shadow: 0 0 6px var(--accent-primary);
  animation: moveParticle 6s linear infinite;
}

@keyframes moveParticle {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg);
    opacity: 0;
  }
  5% {
    opacity: 0.8;
  }
  95% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-110vh) translateX(30px) rotate(180deg);
    opacity: 0;
  }
}

/* Star particle animation with trail effect */
@keyframes moveStarParticle {
  0% {
    transform: translateY(0) translateX(0) scale(0.5);
    opacity: 0;
    box-shadow: 0 0 0px currentColor, 0 0 0px currentColor;
  }
  5% {
    opacity: 1;
    box-shadow: 0 0 6px currentColor, 0 0 12px currentColor;
  }
  15% {
    transform: translateY(-15vh) translateX(20px) scale(1);
    box-shadow: 0 0 8px currentColor, 0 0 16px currentColor, 10px -10px 4px rgba(255,255,255,0.3);
  }
  85% {
    opacity: 1;
    box-shadow: 0 0 8px currentColor, 0 0 16px currentColor, 20px -20px 6px rgba(255,255,255,0.2);
  }
  95% {
    box-shadow: 0 0 4px currentColor, 0 0 8px currentColor, 30px -30px 8px rgba(255,255,255,0.1);
  }
  100% {
    transform: translateY(-100vh) translateX(150px) scale(0.3);
    opacity: 0;
    box-shadow: 0 0 0px currentColor, 0 0 0px currentColor;
  }
}

/* Diagonal movement */
@keyframes moveDiagonalParticle {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  90% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100vh) translateX(80px) rotate(360deg);
    opacity: 0;
  }
}

/* Curved movement */
@keyframes moveCurvedParticle {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg);
    opacity: 0;
  }
  25% {
    transform: translateY(-25vh) translateX(-30px) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-50vh) translateX(0) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-75vh) translateX(30px) rotate(270deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) translateX(0) rotate(360deg);
    opacity: 0;
  }
}

[data-theme="dark"] .particle {
  animation: moveParticleDark 15s linear infinite;
}

[data-theme="dark"] .particle:nth-child(11n) {
  animation: moveStarParticleDark 8s linear infinite;
}

[data-theme="dark"] .particle:nth-child(13n) {
  animation: moveStarParticleDark 10s linear infinite;
}

@keyframes moveParticleDark {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.9;
  }
  90% {
    opacity: 0.9;
  }
  100% {
    transform: translateY(-100vh) translateX(30px) rotate(180deg);
    opacity: 0;
  }
}

@keyframes moveStarParticleDark {
  0% {
    transform: translateY(0) translateX(0) scale(0.5);
    opacity: 0;
    box-shadow: 0 0 0px currentColor, 0 0 0px currentColor;
  }
  5% {
    opacity: 1;
    box-shadow: 0 0 8px currentColor, 0 0 16px currentColor;
  }
  15% {
    transform: translateY(-15vh) translateX(20px) scale(1);
    box-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 10px -10px 6px rgba(255,255,255,0.4);
  }
  85% {
    opacity: 1;
    box-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 20px -20px 8px rgba(255,255,255,0.3);
  }
  95% {
    box-shadow: 0 0 6px currentColor, 0 0 12px currentColor, 30px -30px 10px rgba(255,255,255,0.2);
  }
  100% {
    transform: translateY(-100vh) translateX(150px) scale(0.3);
    opacity: 0;
    box-shadow: 0 0 0px currentColor, 0 0 0px currentColor;
  }
}

/* Random snow-like positions and animations for particles */
.particle-1 { left: 3%; top: 85%; animation-delay: 0s; animation-duration: 12s; }
.particle-2 { left: 97%; top: 23%; animation-delay: 1.5s; animation-duration: 18s; animation-name: moveDiagonalParticle; }
.particle-3 { left: 15%; top: 67%; animation-delay: 3s; animation-duration: 15s; animation-name: moveCurvedParticle; }
.particle-4 { left: 82%; top: 41%; animation-delay: 0.8s; animation-duration: 20s; }
.particle-5 { left: 45%; top: 92%; animation-delay: 4.2s; animation-duration: 14s; animation-name: moveDiagonalParticle; }
.particle-6 { left: 71%; top: 18%; animation-delay: 2.1s; animation-duration: 16s; }
.particle-7 { left: 28%; top: 76%; animation-delay: 5.5s; animation-duration: 13s; animation-name: moveCurvedParticle; }
.particle-8 { left: 89%; top: 54%; animation-delay: 1.2s; animation-duration: 19s; }
.particle-9 { left: 6%; top: 31%; animation-delay: 6.8s; animation-duration: 11s; animation-name: moveDiagonalParticle; }
.particle-10 { left: 64%; top: 88%; animation-delay: 3.7s; animation-duration: 17s; }
.particle-11 { left: 37%; top: 12%; animation-delay: 7.3s; animation-duration: 9s; }
.particle-12 { left: 93%; top: 69%; animation-delay: 2.9s; animation-duration: 21s; animation-name: moveCurvedParticle; }
.particle-13 { left: 19%; top: 45%; animation-delay: 8.1s; animation-duration: 10s; }
.particle-14 { left: 76%; top: 83%; animation-delay: 4.6s; animation-duration: 22s; animation-name: moveDiagonalParticle; }
.particle-15 { left: 52%; top: 27%; animation-delay: 9.2s; animation-duration: 13s; animation-name: moveCurvedParticle; }
.particle-16 { left: 11%; top: 91%; animation-delay: 1.8s; animation-duration: 25s; }
.particle-17 { left: 85%; top: 36%; animation-delay: 6.4s; animation-duration: 8s; animation-name: moveDiagonalParticle; }
.particle-18 { left: 42%; top: 74%; animation-delay: 3.3s; animation-duration: 18s; }
.particle-19 { left: 68%; top: 19%; animation-delay: 7.9s; animation-duration: 14s; animation-name: moveCurvedParticle; }
.particle-20 { left: 24%; top: 58%; animation-delay: 5.1s; animation-duration: 20s; }
.particle-21 { left: 91%; top: 82%; animation-delay: 2.7s; animation-duration: 16s; animation-name: moveDiagonalParticle; }
.particle-22 { left: 33%; top: 14%; animation-delay: 8.5s; animation-duration: 12s; }
.particle-23 { left: 79%; top: 63%; animation-delay: 4.3s; animation-duration: 24s; animation-name: moveCurvedParticle; }
.particle-24 { left: 8%; top: 47%; animation-delay: 1.6s; animation-duration: 15s; }
.particle-25 { left: 56%; top: 89%; animation-delay: 6.7s; animation-duration: 19s; animation-name: moveDiagonalParticle; }
.particle-26 { left: 73%; top: 25%; animation-delay: 9.8s; animation-duration: 11s; }
.particle-27 { left: 17%; top: 71%; animation-delay: 2.4s; animation-duration: 23s; animation-name: moveCurvedParticle; }
.particle-28 { left: 94%; top: 38%; animation-delay: 7.6s; animation-duration: 17s; }
.particle-29 { left: 39%; top: 86%; animation-delay: 4.9s; animation-duration: 21s; animation-name: moveDiagonalParticle; }
.particle-30 { left: 61%; top: 52%; animation-delay: 8.7s; animation-duration: 14s; }
.particle-15 { left: 50%; animation-delay: 14s; }
.particle-16 { left: 60%; animation-delay: 0.5s; }
.particle-17 { left: 70%; animation-delay: 1.5s; }
.particle-18 { left: 80%; animation-delay: 2.5s; }
.particle-19 { left: 90%; animation-delay: 3.5s; }
.particle-20 { left: 12%; animation-delay: 4.5s; }
