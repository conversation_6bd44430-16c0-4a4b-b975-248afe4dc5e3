.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: flex-start;
  overflow: hidden;
  padding-top: 100px; /* <PERSON><PERSON> + <PERSON><PERSON> b<PERSON> */
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, var(--accent-primary)15 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, var(--accent-cyan)10 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, var(--accent-pink)12 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, var(--accent-gold)08 0%, transparent 50%),
    linear-gradient(45deg, transparent 30%, var(--accent-primary)02 50%, transparent 70%);
  opacity: 0.15;
  animation: float 6s ease-in-out infinite;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%);
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
  position: relative;
  z-index: 1;
  padding-top: 0;
}

.hero-text {
  max-width: 600px;
  text-align: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: var(--gradient-primary);
  background-size: 200% auto;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: textShimmer 4s linear infinite;
  position: relative;
}

.hero-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 2px;
  animation: slideInCenter 1s ease-out 0.5s both;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.hero-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-weight: 500;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: 1rem;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  text-align: center;
  min-height: 100px;
}

.feature-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-primary);
}

.hero-features .feature-item .feature-icon {
  color: var(--accent-orange) !important;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-cta {
  margin-top: 2rem;
}

/* Mobile için CTA butonuna ekstra spacing */
@media (max-width: 768px) {
  .hero-cta {
    margin-top: 2.5rem;
    padding-top: 1rem;
  }
}

@media (max-width: 480px) {
  .hero-cta {
    margin-top: 2rem;
    padding-top: 1.25rem;
  }
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 3rem; /* Giriş yap kartını aşağıya kaydır */
}

.hero-card {
  background: linear-gradient(145deg, var(--bg-secondary), var(--bg-primary));
  border: 1px solid var(--border-color);
  border-radius: 2rem;
  padding: 2.5rem;
  box-shadow: var(--shadow-xl), 0 0 40px rgba(99, 102, 241, 0.1);
  backdrop-filter: blur(20px);
  width: 100%;
  max-width: 420px;
  position: relative;
  overflow: hidden;
  animation: float 4s ease-in-out infinite;
}

.hero-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 2rem 2rem 0 0;
}

.hero-card::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, var(--accent-primary)05 0%, transparent 70%);
  animation: float 6s ease-in-out infinite reverse;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.dashboard-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dashboard-icon {
  color: var(--accent-primary);
  width: 1.5rem;
  height: 1.5rem;
}

.card-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--accent-green);
  font-size: 0.875rem;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  background-color: var(--accent-green);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.card-content {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.metric {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-tertiary);
  border-radius: 1rem;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.metric:hover {
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.metric.highlight-metric {
  background: linear-gradient(135deg, var(--accent-primary)10, var(--accent-cyan)05);
  border-color: var(--accent-primary);
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: var(--gradient-primary);
  border-radius: 0.75rem;
  flex-shrink: 0;
}

.metric-icon-svg {
  color: white;
  width: 1.25rem;
  height: 1.25rem;
}

.metric-info {
  flex: 1;
}

.metric-label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.chart-placeholder {
  height: 120px;
  background: var(--bg-tertiary);
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.chart-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, var(--accent-primary)05, var(--accent-cyan)03);
  border-radius: 0.75rem;
}

.hero-chart {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
}

.chart-bars {
  display: flex;
  gap: 0.5rem;
  align-items: end;
  width: 100%;
  height: 100%;
}

.bar {
  flex: 1;
  background: linear-gradient(to top, var(--accent-primary), var(--accent-secondary));
  border-radius: 0.25rem;
  min-height: 20%;
  animation: growUp 1s ease-out;
}

@keyframes growUp {
  from {
    height: 0;
  }
  to {
    height: var(--final-height, 100%);
  }
}

@keyframes slideInCenter {
  from {
    opacity: 0;
    transform: translateX(-50%) scale(0.5);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
}

/* Mobile pulse animation for hero title */
@keyframes mobilePulse {
  0%, 100% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.02);
    filter: brightness(1.1);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
    padding-top: 0;
  }
  
  .hero-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: auto;
    padding-top: 20px; /* Çok az boşluk */
    padding-bottom: 4rem; /* Giriş kartı için daha fazla alan */
  }

  .hero-title {
    font-size: 3rem; /* Daha da büyük font */
    line-height: 1.05; /* Daha sıkı satır aralığı */
    margin-bottom: 1.5rem; /* Daha fazla margin */
    text-shadow: 0 2px 10px rgba(99, 102, 241, 0.3); /* Glow efekti */
    animation: textShimmer 3s linear infinite, mobilePulse 2s ease-in-out infinite; /* Çift animasyon */
  }

  .hero-title::after {
    width: 100px; /* Daha uzun çizgi */
    height: 4px; /* Daha kalın çizgi */
    bottom: -12px; /* Daha aşağıda */
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.5); /* Glow efekti */
  }

  .hero-subtitle {
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 2rem;
    padding-top: 35px;
    padding-bottom: 25px;
  }

  .hero-features {
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .feature-item {
    font-size: 0.85rem;
  }

  .hero-visual {
    margin-top: 6rem; /* Mobile'da giriş kartını çok daha aşağıya taşı */
  }

  .hero-card {
    padding: 1.25rem;
    margin-top: 2rem;
  }

  .card-header {
    margin-bottom: 1.5rem;
  }

  .dashboard-title h3 {
    font-size: 1rem;
  }

  .metric {
    padding: 0.75rem;
  }

  .metric-label {
    font-size: 0.8rem;
  }

  .metric-value {
    font-size: 1.125rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding-top: 20px; /* Çok az boşluk */
    padding-bottom: 3rem; /* Giriş kartı için yeterli alan */
  }

  .hero-title {
    font-size: 2.75rem; /* Daha da büyük font küçük ekranlarda */
    line-height: 1.0; /* Daha sıkı satır aralığı */
    text-shadow: 0 2px 15px rgba(99, 102, 241, 0.4); /* Daha güçlü glow */
    animation: textShimmer 2.5s linear infinite, mobilePulse 1.8s ease-in-out infinite; /* Daha hızlı animasyon */
  }

  .hero-title::after {
    width: 80px; /* Daha uzun çizgi */
    height: 3.5px; /* Daha kalın çizgi */
    bottom: -10px; /* Daha aşağıda */
    box-shadow: 0 0 15px rgba(99, 102, 241, 0.6); /* Daha güçlü glow */
  }

  .hero-subtitle {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    padding-top: 35px;
    padding-bottom: 25px;
  }

  .hero-features {
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
  }

  .feature-item {
    font-size: 0.8rem;
  }

  .hero-visual {
    margin-top: 5rem; /* Küçük ekranlarda giriş kartını çok daha aşağıya taşı */
  }

  .hero-card {
    padding: 1rem;
    margin-top: 1.5rem;
  }

  .dashboard-title h3 {
    font-size: 0.9rem;
  }

  .metric {
    padding: 0.625rem;
  }

  .metric-label {
    font-size: 0.75rem;
  }

  .metric-value {
    font-size: 1rem;
  }

  .highlight-metric .metric-value {
    font-size: 1.125rem;
  }
}
