.auth-card {
  background: linear-gradient(145deg, var(--bg-secondary), var(--bg-primary));
  border: 1px solid var(--border-color);
  border-radius: 1.5rem;
  padding: 1.5rem;
  box-shadow: var(--shadow-xl), 0 0 40px rgba(99, 102, 241, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  animation: fadeInUp 1s ease-out 0.3s both, floatAnimation 3s ease-in-out infinite;
  max-width: 400px;
  width: 100%;
}

.auth-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 2rem 2rem 0 0;
}

.auth-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 2;
}

.auth-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.auth-icon {
  color: var(--accent-primary);
  width: 1.5rem;
  height: 1.5rem;
}

.auth-title h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: var(--text-muted);
}

.status-dot {
  width: 8px;
  height: 8px;
  background-color: var(--accent-green);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.auth-content {
  position: relative;
  z-index: 2;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-row {
  display: flex;
  gap: 0.75rem;
}

.form-row .form-group {
  flex: 1;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 0.75rem;
  width: 1rem;
  height: 1rem;
  color: var(--text-muted);
  z-index: 1;
}

.auth-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background: var(--bg-tertiary);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.auth-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.auth-input.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.auth-input.error:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
}

.password-toggle {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: var(--accent-primary);
}

.auth-submit-btn {
  width: 100%;
  padding: 0.875rem;
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 0.5rem;
}

.auth-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

.auth-divider {
  display: flex;
  align-items: center;
  margin: 1rem 0;
  color: var(--text-muted);
  font-size: 0.85rem;
}

.auth-divider::before,
.auth-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--border-color);
}

.auth-divider span {
  padding: 0 1rem;
}

.google-auth-btn {
  width: 100%;
  padding: 0.875rem;
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.google-auth-btn:hover {
  background: var(--bg-secondary);
  border-color: var(--accent-primary);
  transform: translateY(-1px);
}

.google-icon {
  font-size: 1.1rem;
}

.auth-switch {
  text-align: center;
  font-size: 0.85rem;
  color: var(--text-muted);
  margin-bottom: 1rem;
}

.auth-switch-btn {
  background: none;
  border: none;
  color: var(--accent-primary);
  cursor: pointer;
  font-weight: 600;
  margin-left: 0.25rem;
  transition: color 0.3s ease;
}

.auth-switch-btn:hover {
  color: var(--accent-tertiary);
}

.id-requirement {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
  padding: 0.5rem;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 0.375rem;
  line-height: 1.4;
}

.id-requirement strong {
  color: var(--accent-warning, #ffc107);
}

.password-requirements {
  font-size: 0.75rem;
  margin-top: 0.5rem;
  padding: 0.75rem;
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 0.375rem;
  line-height: 1.4;
}

.password-requirements strong {
  color: var(--accent-primary);
  display: block;
  margin-bottom: 0.5rem;
}

.password-requirement-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.password-requirement-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  color: var(--text-muted);
  transition: color 0.3s ease;
}

.password-requirement-item.valid {
  color: var(--accent-green, #10b981);
}

.password-requirement-item.invalid {
  color: var(--accent-error, #ef4444);
}

.requirement-icon {
  font-size: 0.875rem;
  font-weight: bold;
}

.login-error {
  padding: 0.75rem 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid #ef4444;
  border-radius: 0.5rem;
  color: #ef4444;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1rem;
  text-align: center;
}



@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .auth-card {
    padding: 1.25rem;
    margin-top: 2rem;
  }

  .auth-title h3 {
    font-size: 1rem;
  }

  .auth-input {
    padding: 0.625rem 0.625rem 0.625rem 2.25rem;
    font-size: 0.85rem;
  }

  .input-icon {
    left: 0.625rem;
    width: 0.9rem;
    height: 0.9rem;
  }

  .auth-submit-btn,
  .google-auth-btn {
    padding: 0.75rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .auth-card {
    padding: 1rem;
    margin-top: 1.5rem;
  }

  .auth-title h3 {
    font-size: 0.95rem;
  }

  .auth-input {
    padding: 0.5rem 0.5rem 0.5rem 2rem;
    font-size: 0.8rem;
  }

  .input-icon {
    left: 0.5rem;
    width: 0.8rem;
    height: 0.8rem;
  }

  .auth-submit-btn,
  .google-auth-btn {
    padding: 0.625rem;
    font-size: 0.8rem;
  }
}

/* Float Animation for AuthCard */
@keyframes floatAnimation {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
