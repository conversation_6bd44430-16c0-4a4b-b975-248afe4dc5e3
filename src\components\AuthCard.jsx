import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { User, Mail, Lock, Eye, EyeOff } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import './AuthCard.css';

const AuthCard = () => {
  const { t, i18n } = useTranslation();
  const { login } = useAuth();
  const [isLogin, setIsLogin] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState({});
  const [loginError, setLoginError] = useState('');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Hata varsa temizle
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: false
      });
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setLoginError('');

    if (isLogin) {
      // Login işlemi
      const result = login(formData.email, formData.password);
      if (!result.success) {
        setLoginError(t('auth.login_error'));
      }
    } else {
      // Register işlemi
      if (validateForm()) {
        console.log('Registration form submitted:', formData);
        // Register başarılı mesajı gösterilebilir
      }
    }
  };

  const handleGoogleAuth = () => {
    console.log('Google auth clicked');
  };

  const toggleAuthMode = () => {
    setIsLogin(!isLogin);
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: ''
    });
    setErrors({}); // Hataları temizle
  };



  // Şifre güçlülük kontrolü
  const validatePassword = (password) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return {
      isValid: password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar,
      checks: {
        minLength: password.length >= minLength,
        hasUpperCase,
        hasLowerCase,
        hasNumbers,
        hasSpecialChar
      }
    };
  };

  // Form validation
  const validateForm = () => {
    const newErrors = {};

    if (!isLogin) {
      if (!formData.firstName.trim()) {
        newErrors.firstName = true;
      }
      if (!formData.lastName.trim()) {
        newErrors.lastName = true;
      }
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = true;
      }
    }

    if (!formData.email.trim() || !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = true;
    }

    const passwordValidation = validatePassword(formData.password);
    if (!formData.password.trim() || !passwordValidation.isValid) {
      newErrors.password = passwordValidation.checks;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  return (
    <div className="auth-card">
      <div className="auth-header">
        <div className="auth-title">
          <User className="auth-icon" />
          <h3>{isLogin ? t('auth.login.title') : t('auth.register.title')}</h3>
        </div>
        <div className="status-indicator">
          <div className="status-dot"></div>
          <span>{t('auth.secure')}</span>
        </div>
      </div>

      <div className="auth-content">
        <form onSubmit={handleSubmit} className="auth-form">
          {!isLogin && (
            <>
              <div className="form-row">
                <div className="form-group">
                  <div className="input-wrapper">
                    <User className="input-icon" />
                    <input
                      type="text"
                      name="firstName"
                      placeholder={t('auth.first_name_placeholder')}
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className="auth-input"
                      required
                    />
                  </div>
                </div>
                <div className="form-group">
                  <div className="input-wrapper">
                    <User className="input-icon" />
                    <input
                      type="text"
                      name="lastName"
                      placeholder={t('auth.last_name_placeholder')}
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className="auth-input"
                      required
                    />
                  </div>
                </div>
              </div>
            </>
          )}

          <div className="form-group">
            <div className="input-wrapper">
              <Mail className="input-icon" />
              <input
                type="email"
                name="email"
                placeholder={t('auth.email_placeholder')}
                value={formData.email}
                onChange={handleInputChange}
                className="auth-input"
                required
              />
            </div>
          </div>

          <div className="form-group">
            <div className="input-wrapper">
              <Lock className="input-icon" />
              <input
                type={showPassword ? 'text' : 'password'}
                name="password"
                placeholder={t('auth.password_placeholder')}
                value={formData.password}
                onChange={handleInputChange}
                className={`auth-input ${errors.password ? 'error' : ''}`}
                required
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
            {!isLogin && formData.password && (
              <div className="password-requirements">
                <strong dangerouslySetInnerHTML={{ __html: t('auth.password_requirements') }} />
                <ul className="password-requirement-list">
                  <li className={`password-requirement-item ${validatePassword(formData.password).checks.minLength ? 'valid' : 'invalid'}`}>
                    <span className="requirement-icon">{validatePassword(formData.password).checks.minLength ? '✓' : '✗'}</span>
                    {t('auth.password_min_length')}
                  </li>
                  <li className={`password-requirement-item ${validatePassword(formData.password).checks.hasUpperCase ? 'valid' : 'invalid'}`}>
                    <span className="requirement-icon">{validatePassword(formData.password).checks.hasUpperCase ? '✓' : '✗'}</span>
                    {t('auth.password_uppercase')}
                  </li>
                  <li className={`password-requirement-item ${validatePassword(formData.password).checks.hasLowerCase ? 'valid' : 'invalid'}`}>
                    <span className="requirement-icon">{validatePassword(formData.password).checks.hasLowerCase ? '✓' : '✗'}</span>
                    {t('auth.password_lowercase')}
                  </li>
                  <li className={`password-requirement-item ${validatePassword(formData.password).checks.hasNumbers ? 'valid' : 'invalid'}`}>
                    <span className="requirement-icon">{validatePassword(formData.password).checks.hasNumbers ? '✓' : '✗'}</span>
                    {t('auth.password_number')}
                  </li>
                  <li className={`password-requirement-item ${validatePassword(formData.password).checks.hasSpecialChar ? 'valid' : 'invalid'}`}>
                    <span className="requirement-icon">{validatePassword(formData.password).checks.hasSpecialChar ? '✓' : '✗'}</span>
                    {t('auth.password_special')}
                  </li>
                </ul>
              </div>
            )}
          </div>

          {!isLogin && (
            <div className="form-group">
              <div className="input-wrapper">
                <Lock className="input-icon" />
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="confirmPassword"
                  placeholder={t('auth.confirm_password_placeholder')}
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className="auth-input"
                  required
                />
              </div>
            </div>
          )}

          {loginError && (
            <div className="login-error">
              {loginError}
            </div>
          )}

          <button type="submit" className="auth-submit-btn">
            {isLogin ? t('auth.login.submit') : t('auth.register.submit')}
          </button>
        </form>

        <div className="auth-divider">
          <span>{t('auth.or')}</span>
        </div>

        <button onClick={handleGoogleAuth} className="google-auth-btn">
          <span className="google-icon">🔍</span>
          {isLogin ? t('auth.google.login') : t('auth.google.register')}
        </button>

        <div className="auth-switch">
          <span>
            {isLogin ? t('auth.no_account') : t('auth.have_account')}
          </span>
          <button onClick={toggleAuthMode} className="auth-switch-btn">
            {isLogin ? t('auth.register.link') : t('auth.login.link')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AuthCard;
