import React from 'react';
import { useTranslation } from 'react-i18next';
import { Shield, Zap, TrendingUp, Users } from 'lucide-react';
import { CryptoChart, SecurityShield, GlobalNetwork, TrendingArrow } from './FinancialIcons';
import AnimatedBackground from './AnimatedBackground';
import './TrustSection.css';

const TrustSection = () => {
  const { t } = useTranslation();

  const advantages = [
    {
      icon: <SecurityShield className="advantage-icon-svg" />,
      title: t('trust.advantage1.title'),
      description: t('trust.advantage1.description')
    },
    {
      icon: <TrendingArrow className="advantage-icon-svg" />,
      title: t('trust.advantage2.title'),
      description: t('trust.advantage2.description')
    },
    {
      icon: <CryptoChart className="advantage-icon-svg" />,
      title: t('trust.advantage3.title'),
      description: t('trust.advantage3.description')
    },
    {
      icon: <GlobalNetwork className="advantage-icon-svg" />,
      title: t('trust.advantage4.title'),
      description: t('trust.advantage4.description')
    }
  ];

  return (
    <section id="trust" className="trust-section section">
      <AnimatedBackground variant="particles" />
      <div className="container">
        <div className="trust-content">
          <div className="trust-text">
            <h2 className="section-title text-shimmer fade-in-up">
              {t('trust.title')}
            </h2>
            <h3 className="section-subtitle fade-in-up">
              {t('trust.subtitle')}
            </h3>
            <p className="trust-description fade-in-up">
              {t('trust.description')}
            </p>
            
            <div className="comparison-highlight fade-in-up">
              <div className="comparison-item">
                <div className="comparison-platform">
                  <img
                    src="/icons/binance.png"
                    alt="Binance"
                    className="platform-logo"
                  />
                  <span>Binance Earn</span>
                </div>
                <div className="comparison-rate low">
                  ~5% <img src="/icons/tether.png" alt="USDT" className="usdt-icon" />
                </div>
              </div>
              
              <div className="vs-divider">VS</div>
              
              <div className="comparison-item highlight">
                <div className="comparison-platform">
                  <div className="novayield-logo">N</div>
                  <span>NovaYield</span>
                </div>
                <div className="comparison-rate high">
                  102% <img src="/icons/tether.png" alt="USDT" className="usdt-icon" />
                </div>
              </div>
            </div>
          </div>

          <div className="trust-visual">
            <div className="advantages-grid">
              {advantages.map((advantage, index) => (
                <div 
                  key={index}
                  className="advantage-card fade-in-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {advantage.icon}
                  <h4>{advantage.title}</h4>
                  <p>{advantage.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrustSection;
