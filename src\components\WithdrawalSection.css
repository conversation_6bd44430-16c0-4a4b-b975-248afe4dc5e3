.withdrawal-section {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.withdrawal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.withdrawal-header h4 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  color: var(--text-primary);
  font-size: 1.125rem;
}

.next-payment {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-muted);
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
}

.withdrawal-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.withdrawal-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 1.25rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.withdrawal-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  border-radius: 0.75rem 0.75rem 0 0;
}

.withdrawal-card.accumulating::before {
  background: var(--accent-orange);
}

.withdrawal-card.available::before {
  background: var(--accent-green);
}

.withdrawal-card.withdrawn::before {
  background: var(--accent-primary);
}

.withdrawal-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

.card-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.card-description {
  color: var(--text-muted);
  font-size: 0.8rem;
  line-height: 1.4;
  margin-bottom: 1rem;
}

.withdraw-btn,
.history-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
}

.withdraw-btn {
  background: var(--accent-green);
  color: white;
}

.withdraw-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.history-btn {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.history-btn:hover {
  background: var(--accent-primary);
  color: white;
}

.no-history {
  text-align: center;
  color: var(--text-muted);
  font-size: 0.875rem;
  font-style: italic;
  padding: 0.75rem;
}

.monthly-payment-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.payment-amount {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.payment-amount svg {
  color: var(--accent-primary);
}

.payment-label {
  display: block;
  color: var(--text-muted);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.payment-value {
  display: block;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.payment-schedule {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.schedule-label {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.schedule-value {
  color: var(--text-primary);
  font-weight: 500;
}

/* Çekim Geçmişi Modal */
.withdraw-history-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  backdrop-filter: blur(4px);
}

.history-content {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.history-header h3 {
  margin: 0;
  color: var(--text-primary);
}

.close-history {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--bg-secondary);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-history:hover {
  background: #ef4444;
  color: white;
}

.history-list {
  padding: 1rem;
}

.history-item {
  padding: 1.5rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  margin-bottom: 1rem;
}

.history-item:last-child {
  margin-bottom: 0;
}

.history-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.history-amount {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--accent-green);
}

.history-date {
  color: var(--text-muted);
  font-size: 0.875rem;
}

.history-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.detail-label {
  color: var(--text-muted);
  font-size: 0.875rem;
  min-width: 100px;
}

.detail-value {
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
}

.network-badge {
  color: white !important;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.address-container,
.tx-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.address,
.tx-hash {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
}

.copy-address,
.copy-tx {
  padding: 0.25rem;
  border: none;
  background: var(--bg-primary);
  border-radius: 0.25rem;
  color: var(--text-muted);
  cursor: pointer;
  transition: all 0.3s ease;
}

.copy-address:hover,
.copy-tx:hover {
  background: var(--accent-primary);
  color: white;
}

.copy-address.copied,
.copy-tx.copied {
  background: var(--accent-green);
  color: white;
}

.view-tx {
  padding: 0.25rem;
  color: var(--accent-primary);
  text-decoration: none;
  border-radius: 0.25rem;
  transition: all 0.3s ease;
}

.view-tx:hover {
  background: var(--accent-primary);
  color: white;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.status.completed {
  background: var(--accent-green);
  color: white;
}

.status.pending {
  background: var(--accent-orange);
  color: white;
}

.status.failed {
  background: #ef4444;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .withdrawal-grid {
    grid-template-columns: 1fr;
  }
  
  .monthly-payment-info {
    flex-direction: column;
    align-items: stretch;
  }
  
  .payment-schedule {
    align-items: flex-start;
  }
  
  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .detail-label {
    min-width: auto;
  }
}
