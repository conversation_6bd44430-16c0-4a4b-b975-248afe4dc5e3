/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-primary);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  transition: all 0.3s ease;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 800;
}

/* Navigation */
.navigation {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.desktop-nav {
  display: flex;
}

.mobile-navigation {
  display: none;
  flex-direction: column;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: 1rem 0;
  gap: 0.5rem;
  animation: slideDown 0.3s ease-out;
  backdrop-filter: blur(10px);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  display: block;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: var(--accent-primary);
  background: var(--accent-primary)10;
}

.mobile-menu-toggle {
  display: none;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mobile-menu-toggle:hover {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--accent-primary);
  background-color: var(--bg-secondary);
  transform: translateY(-1px);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 80%;
}

.nav-link.active {
  font-weight: 600;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Desktop'ta language selector header-controls içinde */
@media (min-width: 769px) {
  .header-content > .language-selector {
    display: none;
  }

  .header-controls .language-selector {
    display: block;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
  }
}

.language-selector {
  position: relative;
  display: flex;
  align-items: center;
}

.language-dropdown-trigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 2px solid var(--border-color);
  background: var(--bg-secondary);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 4rem;
}

.language-dropdown-trigger:hover {
  border-color: var(--accent-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.language-dropdown-trigger .flag-image {
  width: 1.5rem;
  height: 1.125rem;
  object-fit: cover;
  border-radius: 0.25rem;
}

.dropdown-arrow {
  color: var(--text-secondary);
  transition: transform 0.3s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.language-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  background: var(--bg-secondary);
  border: 2px solid var(--border-color);
  border-radius: 0.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 10rem;
  overflow: hidden;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.language-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.language-option:hover {
  background: var(--bg-tertiary);
}

.language-option.active {
  background: var(--accent-primary);
  color: white;
}

/* Dark mode için dil seçici kontrast iyileştirmesi */
[data-theme="dark"] .language-dropdown {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-xl);
}

[data-theme="dark"] .language-option {
  color: var(--text-primary);
}

[data-theme="dark"] .language-option:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

[data-theme="dark"] .language-option.active {
  background: var(--accent-primary);
  color: white;
}

[data-theme="dark"] .language-name {
  color: var(--text-primary);
  font-weight: 500;
}

.language-option .flag-image {
  width: 1.5rem;
  height: 1.125rem;
  object-fit: cover;
  border-radius: 0.25rem;
}

.language-name {
  font-size: 0.875rem;
  font-weight: 500;
}

.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-toggle:hover {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

/* Main content spacing for fixed header */
main {
  margin-top: 80px;
}

/* Sticky Contact Button */
.sticky-contact-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3.5rem;
  height: 3.5rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
  z-index: 999;
}

.sticky-contact-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--shadow-xl);
}

.sticky-contact-btn:active {
  transform: translateY(0) scale(0.95);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .desktop-nav {
    gap: 1.5rem;
  }

  .nav-link {
    font-size: 0.9rem;
    padding: 0.375rem 0.75rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0.75rem 0;
    gap: 1rem;
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    justify-self: start;
  }

  /* Desktop navigation gizle */
  .desktop-nav {
    display: none;
  }

  /* Mobile menu toggle sadece header-controls içinde göster */
  .mobile-menu-toggle {
    display: flex;
  }

  /* Mobile navigation göster */
  .mobile-navigation {
    display: flex;
  }

  .logo h2 {
    font-size: 1.25rem;
  }

  /* Language selector ortada */
  .header-content > .language-selector {
    justify-self: center;
    display: block;
  }

  .header-controls {
    gap: 0.75rem;
    display: flex;
    align-items: center;
    justify-self: end;
  }

  /* Desktop language selector'ı gizle */
  .header-controls .language-selector {
    display: none;
  }

  .language-dropdown-trigger {
    padding: 0.375rem;
    min-width: 3rem;
  }

  .language-dropdown {
    min-width: 8rem;
    right: 0;
  }

  .language-name {
    font-size: 0.85rem;
    font-weight: 500;
  }

  .theme-toggle {
    width: 2.25rem;
    height: 2.25rem;
  }

  .mobile-menu-toggle {
    width: 2.25rem;
    height: 2.25rem;
  }

  main {
    margin-top: 70px;
  }

  .sticky-contact-btn {
    bottom: 1.5rem;
    right: 1.5rem;
    width: 3rem;
    height: 3rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0.5rem 0;
  }

  .header-left {
    gap: 0.5rem;
  }

  .logo h2 {
    font-size: 1.125rem;
  }

  .header-controls {
    gap: 0.5rem;
  }

  /* Language selector küçük ekranlarda daha kompakt */
  .header-content > .language-selector {
    justify-self: center;
    display: block;
  }

  .header-controls .language-selector {
    display: none;
  }

  .language-dropdown-trigger {
    padding: 0.25rem;
    min-width: 2.75rem;
  }

  .language-dropdown {
    min-width: 7rem;
    right: 0;
  }

  .language-name {
    font-size: 0.8rem;
  }

  .theme-toggle {
    width: 2rem;
    height: 2rem;
  }

  .mobile-menu-toggle {
    width: 2rem;
    height: 2rem;
  }

  .mobile-nav-link {
    font-size: 0.9rem;
    padding: 0.625rem 1rem;
  }

  main {
    margin-top: 65px;
  }

  .sticky-contact-btn {
    bottom: 1rem;
    right: 1rem;
    width: 2.75rem;
    height: 2.75rem;
  }
}

/* Loading Screen */
.loading-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
