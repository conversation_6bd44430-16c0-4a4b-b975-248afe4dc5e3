import express from 'express';
import { authenticateToken, requireVerifiedEmail } from '../middleware/auth.js';
import { validateUpdateProfile, validateChangePassword } from '../middleware/validation.js';
import User from '../models/User.js';
import bcrypt from 'bcryptjs';

const router = express.Router();

// Tüm route'lar authentication gerektirir
router.use(authenticateToken);

// Kullanıcı profili getir
router.get('/profile', async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Kullanıcı bulunamadı'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          phone: user.phone,
          dateOfBirth: user.dateOfBirth,
          country: user.country,
          city: user.city,
          isVerified: user.isVerified,
          role: user.role,
          preferredCurrency: user.preferredCurrency,
          riskTolerance: user.riskTolerance,
          totalInvested: user.totalInvested,
          totalEarnings: user.totalEarnings,
          notifications: user.notifications,
          createdAt: user.createdAt,
          lastLogin: user.lastLogin
        }
      }
    });

  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
});

// Profil güncelle
router.put('/profile', validateUpdateProfile, async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      phone,
      dateOfBirth,
      country,
      city,
      preferredCurrency,
      riskTolerance,
      notifications
    } = req.body;

    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Kullanıcı bulunamadı'
      });
    }

    // Güncellenebilir alanları ayarla
    if (firstName) user.firstName = firstName;
    if (lastName) user.lastName = lastName;
    if (phone) user.phone = phone;
    if (dateOfBirth) user.dateOfBirth = dateOfBirth;
    if (country) user.country = country;
    if (city) user.city = city;
    if (preferredCurrency) user.preferredCurrency = preferredCurrency;
    if (riskTolerance) user.riskTolerance = riskTolerance;
    if (notifications) user.notifications = { ...user.notifications, ...notifications };

    await user.save();

    res.status(200).json({
      success: true,
      message: 'Profil başarıyla güncellendi',
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          phone: user.phone,
          dateOfBirth: user.dateOfBirth,
          country: user.country,
          city: user.city,
          preferredCurrency: user.preferredCurrency,
          riskTolerance: user.riskTolerance,
          notifications: user.notifications
        }
      }
    });

  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
});

// Şifre değiştir
router.post('/change-password', validateChangePassword, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    const user = await User.findById(req.user.userId).select('+password');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Kullanıcı bulunamadı'
      });
    }

    // Mevcut şifreyi kontrol et
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Mevcut şifre yanlış'
      });
    }

    // Yeni şifreyi ayarla
    user.password = newPassword;
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Şifre başarıyla değiştirildi'
    });

  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
});

// Hesap istatistikleri
router.get('/stats', async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Kullanıcı bulunamadı'
      });
    }

    // Investment modelini import etmek gerekir
    // const Investment = await import('../models/Investment.js');
    
    // Şimdilik mock data döndürelim
    const stats = {
      totalInvested: user.totalInvested || 0,
      totalEarnings: user.totalEarnings || 0,
      activeInvestments: 0,
      completedInvestments: 0,
      averageReturn: 0,
      joinDate: user.createdAt,
      lastActivity: user.lastLogin
    };

    res.status(200).json({
      success: true,
      data: { stats }
    });

  } catch (error) {
    console.error('Stats fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
});

// Hesap silme
router.delete('/account', requireVerifiedEmail, async (req, res) => {
  try {
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({
        success: false,
        message: 'Hesap silmek için şifre gereklidir'
      });
    }

    const user = await User.findById(req.user.userId).select('+password');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Kullanıcı bulunamadı'
      });
    }

    // Şifreyi kontrol et
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Geçersiz şifre'
      });
    }

    // Aktif yatırımları kontrol et (gelecekte eklenecek)
    // const activeInvestments = await Investment.find({ 
    //   user: req.user.userId, 
    //   status: 'active' 
    // });
    
    // if (activeInvestments.length > 0) {
    //   return res.status(400).json({
    //     success: false,
    //     message: 'Aktif yatırımlarınız bulunduğu için hesap silinemez'
    //   });
    // }

    // Hesabı deaktif et (tamamen silmek yerine)
    user.isActive = false;
    user.email = `deleted_${Date.now()}_${user.email}`;
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Hesabınız başarıyla silindi'
    });

  } catch (error) {
    console.error('Account deletion error:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
});

export default router;
