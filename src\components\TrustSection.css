.trust-section {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 80px 0;
}

.trust-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 30%, var(--accent-primary)06 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, var(--accent-gold)08 0%, transparent 50%);
  opacity: 0.3;
}

.trust-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 3;
}

.trust-text {
  max-width: 600px;
}

.trust-description {
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--text-secondary);
  margin-bottom: 3rem;
}

.comparison-highlight {
  background-color: var(--bg-primary);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.comparison-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-radius: 0.75rem;
  margin-bottom: 1rem;
}

.comparison-item:last-child {
  margin-bottom: 0;
}

.comparison-item.highlight {
  background: linear-gradient(135deg, var(--accent-primary)10, var(--accent-gold)10);
  border: 1px solid var(--accent-primary);
}

.comparison-platform {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
}

.platform-logo {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
}

.novayield-logo {
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1rem;
}

.comparison-rate {
  font-weight: 700;
  font-size: 1.25rem;
}

.comparison-rate.low {
  color: var(--text-muted);
}

.comparison-rate.high {
  color: var(--accent-green);
  font-size: 1.5rem;
  font-weight: 700;
  animation: earningsPulse 3s ease-in-out infinite;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.usdt-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.vs-divider {
  text-align: center;
  font-weight: 700;
  color: var(--text-muted);
  font-size: 0.875rem;
  margin: 0.5rem 0;
}

.trust-visual {
  display: flex;
  justify-content: center;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  max-width: 500px;
}

.advantage-card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.advantage-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-primary);
}

.advantage-icon {
  width: 2.5rem;
  height: 2.5rem;
  color: var(--accent-primary);
  margin-bottom: 1rem;
}

.advantage-icon-svg {
  width: 3rem;
  height: 3rem;
  margin-bottom: 1rem;
}

.advantage-card h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.advantage-card p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .trust-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
  
  .trust-text {
    text-align: center;
    max-width: none;
  }
}

@media (max-width: 768px) {
  .trust-description {
    font-size: 1rem;
  }
  
  .comparison-highlight {
    padding: 1.5rem;
  }
  
  .comparison-item {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .comparison-rate.high {
    font-size: 1.25rem;
  }
  
  .advantages-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .advantage-card {
    padding: 1.25rem;
  }
  
  .advantage-icon {
    width: 2rem;
    height: 2rem;
  }
}

@media (max-width: 480px) {
  .comparison-highlight {
    padding: 1rem;
    margin: 0 -0.5rem;
  }
  
  .comparison-item {
    padding: 0.75rem;
  }
  
  .platform-logo,
  .novayield-logo {
    width: 1.5rem;
    height: 1.5rem;
  }
  
  .novayield-logo {
    font-size: 0.875rem;
  }
  
  .comparison-platform {
    gap: 0.5rem;
    font-size: 0.875rem;
  }
  
  .comparison-rate {
    font-size: 1rem;
  }
  
  .comparison-rate.high {
    font-size: 1.125rem;
  }
  
  .advantage-card {
    padding: 1rem;
  }
  
  .advantage-icon {
    width: 1.75rem;
    height: 1.75rem;
  }
  
  .advantage-card h4 {
    font-size: 0.875rem;
  }
  
  .advantage-card p {
    font-size: 0.75rem;
  }
}
