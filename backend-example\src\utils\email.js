import nodemailer from 'nodemailer';

// Email transporter olu<PERSON><PERSON>
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    },
    tls: {
      rejectUnauthorized: false
    }
  });
};

// Email template'leri
const getEmailTemplate = (type, data) => {
  const baseStyle = `
    <style>
      body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
      .container { max-width: 600px; margin: 0 auto; padding: 20px; }
      .header { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
      .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
      .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
      .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }
    </style>
  `;

  switch (type) {
    case 'verification':
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Email Doğrulama - NovaYield</title>
          ${baseStyle}
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">NovaYield</div>
              <h2>Email Adresinizi Doğrulayın</h2>
            </div>
            <div class="content">
              <h3>Merhaba ${data.firstName}!</h3>
              <p>NovaYield'e hoş geldiniz! Hesabınızı aktifleştirmek için email adresinizi doğrulamanız gerekiyor.</p>
              <p>Aşağıdaki butona tıklayarak email doğrulamasını tamamlayabilirsiniz:</p>
              <div style="text-align: center;">
                <a href="${data.verificationUrl}" class="button">Email Adresimi Doğrula</a>
              </div>
              <p>Eğer buton çalışmıyorsa, aşağıdaki linki tarayıcınıza kopyalayın:</p>
              <p style="word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 5px;">
                ${data.verificationUrl}
              </p>
              <p><strong>Not:</strong> Bu link 24 saat geçerlidir.</p>
              <p>Eğer bu hesabı siz oluşturmadıysanız, bu emaili görmezden gelebilirsiniz.</p>
            </div>
            <div class="footer">
              <p>© 2024 NovaYield. Tüm hakları saklıdır.</p>
              <p>Bu otomatik bir emaildir, lütfen yanıtlamayın.</p>
            </div>
          </div>
        </body>
        </html>
      `;

    case 'password-reset':
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Şifre Sıfırlama - NovaYield</title>
          ${baseStyle}
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">NovaYield</div>
              <h2>Şifre Sıfırlama</h2>
            </div>
            <div class="content">
              <h3>Merhaba ${data.firstName}!</h3>
              <p>Hesabınız için şifre sıfırlama talebinde bulundunuz.</p>
              <p>Yeni şifre oluşturmak için aşağıdaki butona tıklayın:</p>
              <div style="text-align: center;">
                <a href="${data.resetUrl}" class="button">Şifremi Sıfırla</a>
              </div>
              <p>Eğer buton çalışmıyorsa, aşağıdaki linki tarayıcınıza kopyalayın:</p>
              <p style="word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 5px;">
                ${data.resetUrl}
              </p>
              <p><strong>Önemli:</strong> Bu link sadece 10 dakika geçerlidir.</p>
              <p>Eğer şifre sıfırlama talebinde bulunmadıysanız, bu emaili görmezden gelebilirsiniz. Hesabınız güvende kalacaktır.</p>
            </div>
            <div class="footer">
              <p>© 2024 NovaYield. Tüm hakları saklıdır.</p>
              <p>Bu otomatik bir emaildir, lütfen yanıtlamayın.</p>
            </div>
          </div>
        </body>
        </html>
      `;

    case 'welcome':
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Hoş Geldiniz - NovaYield</title>
          ${baseStyle}
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">NovaYield</div>
              <h2>Hoş Geldiniz!</h2>
            </div>
            <div class="content">
              <h3>Merhaba ${data.firstName}!</h3>
              <p>NovaYield ailesine katıldığınız için teşekkür ederiz! 🎉</p>
              <p>Artık USDT ile yüksek getiri elde etmeye başlayabilirsiniz.</p>
              <h4>Neler yapabilirsiniz:</h4>
              <ul>
                <li>✅ Farklı yatırım paketlerini inceleyin</li>
                <li>✅ Yıllık %102'ye varan getiri elde edin</li>
                <li>✅ Güvenli blockchain ağları kullanın</li>
                <li>✅ 7/24 müşteri desteğinden faydalanın</li>
              </ul>
              <div style="text-align: center;">
                <a href="${data.dashboardUrl}" class="button">Yatırıma Başla</a>
              </div>
              <p>Herhangi bir sorunuz olursa, destek ekibimizle iletişime geçmekten çekinmeyin.</p>
            </div>
            <div class="footer">
              <p>© 2024 NovaYield. Tüm hakları saklıdır.</p>
              <p>Destek: <EMAIL> | WhatsApp: +90 540 543 33 22</p>
            </div>
          </div>
        </body>
        </html>
      `;

    default:
      return '';
  }
};

// Email doğrulama gönder
export const sendVerificationEmail = async (email, token, firstName) => {
  try {
    const transporter = createTransporter();
    
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email/${token}`;
    
    const mailOptions = {
      from: `"NovaYield" <${process.env.EMAIL_USER}>`,
      to: email,
      subject: 'Email Adresinizi Doğrulayın - NovaYield',
      html: getEmailTemplate('verification', {
        firstName,
        verificationUrl
      })
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Verification email sent:', result.messageId);
    return result;

  } catch (error) {
    console.error('❌ Verification email error:', error);
    throw error;
  }
};

// Şifre sıfırlama emaili gönder
export const sendPasswordResetEmail = async (email, token, firstName) => {
  try {
    const transporter = createTransporter();
    
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password/${token}`;
    
    const mailOptions = {
      from: `"NovaYield" <${process.env.EMAIL_USER}>`,
      to: email,
      subject: 'Şifre Sıfırlama - NovaYield',
      html: getEmailTemplate('password-reset', {
        firstName,
        resetUrl
      })
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Password reset email sent:', result.messageId);
    return result;

  } catch (error) {
    console.error('❌ Password reset email error:', error);
    throw error;
  }
};

// Hoş geldin emaili gönder
export const sendWelcomeEmail = async (email, firstName) => {
  try {
    const transporter = createTransporter();
    
    const dashboardUrl = `${process.env.FRONTEND_URL}/dashboard`;
    
    const mailOptions = {
      from: `"NovaYield" <${process.env.EMAIL_USER}>`,
      to: email,
      subject: 'NovaYield\'e Hoş Geldiniz! 🎉',
      html: getEmailTemplate('welcome', {
        firstName,
        dashboardUrl
      })
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Welcome email sent:', result.messageId);
    return result;

  } catch (error) {
    console.error('❌ Welcome email error:', error);
    throw error;
  }
};

// Email bağlantısını test et
export const testEmailConnection = async () => {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    console.log('✅ Email server connection successful');
    return true;
  } catch (error) {
    console.error('❌ Email server connection failed:', error);
    return false;
  }
};
