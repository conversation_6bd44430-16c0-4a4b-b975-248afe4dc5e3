# NovaYield - USDT Finans Platformu Landing Page

Modern, responsive ve çok dilli USDT finans platformu landing page'i. React + Vite ile geliştirilmiştir.

## 🚀 Özellikler

### 📱 Mobil-Öncelikli Tasarım
- Responsive tasarım (Mobile-First yaklaşım)
- Tüm cihazlarda kusursuz görünüm
- Touch-friendly arayüz

### 🌙 Tema Desteği
- Açık/Koyu mod geçişi
- Kullanıcı tercihini hatırlama
- Smooth geçiş animasyonları

### 🌍 Çok Dilli Destek
- Türkçe ve İngilizce dil desteği
- Otomatik dil algılama
- i18next entegrasyonu

### 🎨 Modern UI/UX
- Gradient tasarım elementleri
- Smooth scroll animasyonları
- Hover efektleri ve micro-interactions
- Loading states ve feedback

### 📊 İnteraktif Bileşenler
- Animasyonlu sayı sayacları
- <PERSON><PERSON><PERSON><PERSON><PERSON>ş<PERSON><PERSON>rma tabloları
- <PERSON> seç<PERSON> ka<PERSON>ı
- <PERSON><PERSON>işim modal'ı

### 🔧 Teknik Özellikler
- React 18 + Vite
- CSS Custom Properties (CSS Variables)
- Intersection Observer API
- Local Storage entegrasyonu
- SEO optimizasyonu

## 🛠️ Kurulum

### Gereksinimler
- Node.js 16+ 
- npm veya yarn

### Adımlar

1. **Projeyi klonlayın**
```bash
git clone <repository-url>
cd novayield
```

2. **Bağımlılıkları yükleyin**
```bash
npm install
```

3. **Geliştirme sunucusunu başlatın**
```bash
npm run dev
```

4. **Tarayıcıda açın**
```
http://localhost:5173
```

## 📁 Proje Yapısı

```
src/
├── components/          # React bileşenleri
│   ├── HeroSection.jsx     # Ana hero bölümü
│   ├── ComparisonSection.jsx # Karşılaştırma tablosu
│   ├── PlansSection.jsx    # Ürün planları
│   ├── TrustSection.jsx    # Güven ve şeffaflık
│   ├── HowToSection.jsx    # Nasıl başlanır
│   ├── Footer.jsx          # Alt bilgi
│   ├── ContactModal.jsx    # İletişim modal'ı
│   ├── Toast.jsx           # Bildirim sistemi
│   ├── LoadingSpinner.jsx  # Yükleme animasyonu
│   └── SEOHead.jsx         # SEO meta tags
├── hooks/               # Custom React hooks
│   └── useScrollAnimation.js # Scroll animasyonları
├── i18n/               # Çok dilli destek
│   └── config.js          # i18next konfigürasyonu
├── App.jsx             # Ana uygulama bileşeni
├── main.jsx            # Uygulama giriş noktası
└── index.css           # Global stiller
```

## 🎯 Sayfa Bölümleri

### 1. Hero Section
- Ana değer teklifi
- Animasyonlu metrikler
- CTA butonu
- Görsel dashboard mockup

### 2. Karşılaştırma Tablosu
- Piyasa karşılaştırması
- Vurgulanan NovaYield avantajı
- Responsive tablo tasarımı

### 3. Ürün Planları
- Esnek ve Kilitli plan seçenekleri
- Özellik karşılaştırması
- CTA butonları

### 4. Güven ve Şeffaflık
- DeFi protokol açıklaması
- Binance karşılaştırması
- Avantaj kartları

### 5. Nasıl Başlanır
- 3 adımlı süreç
- İkon ve açıklamalar
- Son CTA

### 6. Footer
- İletişim bilgileri
- Sosyal medya linkleri
- Newsletter aboneliği
- Yasal linkler

## 🎨 Tasarım Sistemi

### Renkler
```css
/* Light Theme */
--accent-primary: #3b82f6    /* Ana mavi */
--accent-secondary: #1d4ed8  /* Koyu mavi */
--accent-gold: #f59e0b       /* Altın */
--accent-green: #10b981      /* Yeşil */

/* Dark Theme */
/* Otomatik olarak koyu tema renkleri */
```

### Tipografi
- Font: Inter (Google Fonts)
- Responsive font boyutları
- Hierarchy: H1-H6 + body text

### Spacing
- 8px grid sistemi
- Responsive spacing
- Container max-width: 1200px

## 📱 Responsive Breakpoints

```css
/* Mobile First */
@media (max-width: 480px)  { /* Küçük telefon */ }
@media (max-width: 768px)  { /* Telefon */ }
@media (max-width: 1024px) { /* Tablet */ }
@media (min-width: 1025px) { /* Desktop */ }
```

## 🌐 Çok Dilli Destek

### Desteklenen Diller
- 🇹🇷 Türkçe (varsayılan)
- 🇺🇸 İngilizce

### Yeni Dil Ekleme
1. `src/i18n/config.js` dosyasına yeni dil ekleyin
2. Çeviri anahtarlarını tanımlayın
3. Dil seçici butonunu güncelleyin

## 🔧 Özelleştirme

### Tema Renkleri
`src/index.css` dosyasındaki CSS custom properties'i düzenleyin.

### İçerik Güncelleme
`src/i18n/config.js` dosyasındaki çeviri anahtarlarını güncelleyin.

### İletişim Bilgileri
- WhatsApp: `https://wa.me/905405433322`
- Instagram: `https://instagram.com/novayield`
- Email: `<EMAIL>`

## 🚀 Production Build

```bash
npm run build
```

Build dosyaları `dist/` klasöründe oluşturulur.

## 📈 SEO Optimizasyonu

- Meta tags
- Open Graph tags
- Twitter Card tags
- Structured data (JSON-LD)
- Semantic HTML
- Alt texts

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request açın

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 📞 İletişim

- Website: [novayield.com](https://novayield.com)
- Email: <EMAIL>
- WhatsApp: +90 540 543 33 22
