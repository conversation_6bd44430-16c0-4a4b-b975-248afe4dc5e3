import { body, param, query } from 'express-validator';

// Kullanıcı kaydı validasyonu
export const validateRegister = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Geçerli bir email adresi giriniz'),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('Şifre en az 6 karakter olmalıdır')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Şifre en az bir küçük harf, bir büyük harf ve bir rakam içermelidir'),
  
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Ad 2-50 karakter arasında olmalıdır')
    .matches(/^[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+$/)
    .withMessage('Ad sadece harf içerebilir'),
  
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Soyad 2-50 karakter arasında olmalıdır')
    .matches(/^[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+$/)
    .withMessage('Soyad sadece harf içerebilir'),
  
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Geçerli bir telefon numarası giriniz')
];

// Kullanıcı girişi validasyonu
export const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Geçerli bir email adresi giriniz'),
  
  body('password')
    .notEmpty()
    .withMessage('Şifre gereklidir')
];

// Şifre sıfırlama validasyonu
export const validateForgotPassword = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Geçerli bir email adresi giriniz')
];

// Yeni şifre validasyonu
export const validateResetPassword = [
  param('token')
    .notEmpty()
    .withMessage('Reset token gereklidir'),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('Şifre en az 6 karakter olmalıdır')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Şifre en az bir küçük harf, bir büyük harf ve bir rakam içermelidir')
];

// Email doğrulama validasyonu
export const validateEmailVerification = [
  param('token')
    .notEmpty()
    .withMessage('Doğrulama token gereklidir')
];

// Yatırım oluşturma validasyonu
export const validateCreateInvestment = [
  body('packageType')
    .isIn(['starter', 'growth', 'premium', 'enterprise'])
    .withMessage('Geçerli bir paket türü seçiniz'),
  
  body('amount')
    .isNumeric()
    .withMessage('Tutar sayısal olmalıdır')
    .custom((value) => {
      if (value < 100) {
        throw new Error('Minimum yatırım tutarı 100 USDT');
      }
      if (value > 1000000) {
        throw new Error('Maximum yatırım tutarı 1,000,000 USDT');
      }
      return true;
    }),
  
  body('network')
    .isIn(['TRC20', 'ERC20', 'BEP20'])
    .withMessage('Geçerli bir ağ seçiniz'),
  
  body('walletAddress')
    .notEmpty()
    .withMessage('Cüzdan adresi gereklidir')
    .isLength({ min: 26, max: 62 })
    .withMessage('Geçerli bir cüzdan adresi giriniz'),
  
  body('transactionHash')
    .optional()
    .isLength({ min: 64, max: 66 })
    .withMessage('Geçerli bir transaction hash giriniz')
];

// Profil güncelleme validasyonu
export const validateUpdateProfile = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Ad 2-50 karakter arasında olmalıdır')
    .matches(/^[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+$/)
    .withMessage('Ad sadece harf içerebilir'),
  
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Soyad 2-50 karakter arasında olmalıdır')
    .matches(/^[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+$/)
    .withMessage('Soyad sadece harf içerebilir'),
  
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Geçerli bir telefon numarası giriniz'),
  
  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Geçerli bir doğum tarihi giriniz'),
  
  body('country')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Ülke 2-50 karakter arasında olmalıdır'),
  
  body('city')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Şehir 2-50 karakter arasında olmalıdır')
];

// Şifre değiştirme validasyonu
export const validateChangePassword = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Mevcut şifre gereklidir'),
  
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('Yeni şifre en az 6 karakter olmalıdır')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Yeni şifre en az bir küçük harf, bir büyük harf ve bir rakam içermelidir'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Şifre onayı eşleşmiyor');
      }
      return true;
    })
];

// Pagination validasyonu
export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Sayfa numarası 1 veya daha büyük olmalıdır'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit 1-100 arasında olmalıdır')
];

// MongoDB ObjectId validasyonu
export const validateObjectId = (paramName) => [
  param(paramName)
    .isMongoId()
    .withMessage(`Geçerli bir ${paramName} ID giriniz`)
];
