import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, MessageCircle, Instagram, Mail } from 'lucide-react';
import './ContactModal.css';

const ContactModal = ({ isOpen, onClose }) => {
  const { t } = useTranslation();

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const contactOptions = [
    {
      icon: <MessageCircle size={24} />,
      title: t('contact.whatsapp'),
      description: t('contact.whatsapp_desc'),
      href: 'https://wa.me/905405433322',
      color: '#25D366'
    },
    {
      icon: <Instagram size={24} />,
      title: t('contact.instagram'),
      description: t('contact.instagram_desc'),
      href: 'https://instagram.com/novayield',
      color: '#E4405F'
    },
    {
      icon: <Mail size={24} />,
      title: t('contact.email'),
      description: t('contact.email_desc'),
      href: 'mailto:<EMAIL>',
      color: '#3B82F6'
    }
  ];

  if (!isOpen) return null;

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="modal-content">
        <div className="modal-header">
          <h3 className="modal-title">{t('contact.title')}</h3>
          <button 
            className="modal-close"
            onClick={onClose}
            aria-label="Close modal"
          >
            <X size={24} />
          </button>
        </div>

        <div className="modal-body">
          <p className="modal-description">
            {t('contact.description')}
          </p>

          <div className="contact-options">
            {contactOptions.map((option, index) => (
              <a
                key={index}
                href={option.href}
                target="_blank"
                rel="noopener noreferrer"
                className="contact-option"
                onClick={onClose}
              >
                <div 
                  className="contact-icon"
                  style={{ backgroundColor: `${option.color}20`, color: option.color }}
                >
                  {option.icon}
                </div>
                <div className="contact-info">
                  <h4 className="contact-title">{option.title}</h4>
                  <p className="contact-description">{option.description}</p>
                </div>
                <div className="contact-arrow">→</div>
              </a>
            ))}
          </div>

          <div className="modal-footer">
            <p className="footer-note">
              {t('contact.hours')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactModal;
