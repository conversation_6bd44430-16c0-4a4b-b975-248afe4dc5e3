.comparison-section {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 50%, var(--bg-secondary) 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 60px 0;
}

.comparison-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 10% 20%, var(--accent-cyan)08 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, var(--accent-pink)06 0%, transparent 50%);
  opacity: 0.5;
}

.section-header {
  margin-bottom: 4rem;
  position: relative;
  z-index: 2;
}

.comparison-section .section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.comparison-table {
  background: linear-gradient(145deg, var(--bg-primary), var(--bg-secondary));
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: var(--shadow-xl), 0 0 40px rgba(99, 102, 241, 0.1);
  margin-bottom: 2rem;
  border: 1px solid var(--border-color);
  position: relative;
  z-index: 2;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  background: var(--gradient-primary);
  color: white;
  position: relative;
}

.table-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

.header-cell {
  padding: 1.5rem 2rem;
  font-weight: 600;
  font-size: 1.125rem;
  text-align: center;
}

.table-body {
  display: grid;
  gap: 0;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.table-row:hover {
  background-color: var(--bg-secondary);
}

.table-row.highlight {
  background: linear-gradient(135deg, var(--accent-primary)10, var(--accent-gold)10);
  border: 2px solid var(--accent-primary);
  border-bottom: 2px solid var(--accent-primary);
  position: relative;
}

.table-row.highlight::before {
  content: '⭐';
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.25rem;
}

.platform-cell,
.yield-cell {
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
}

.platform-cell {
  justify-content: flex-start;
  gap: 0.75rem;
}

.platform-logo {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.yield-cell {
  justify-content: center;
  gap: 0.75rem;
}

.platform-name {
  font-weight: 500;
  color: var(--text-primary);
}

.table-row.highlight .platform-name {
  font-weight: 700;
  padding-left: 2rem;
}

.yield-value {
  font-weight: 700;
  font-size: 1.125rem;
  color: var(--text-primary);
}

.table-row.highlight .yield-value {
  font-size: 1.25rem;
  color: var(--accent-green);
  font-weight: 700;
  animation: earningsPulse 3s ease-in-out infinite;
}

.yield-icon {
  width: 20px;
  height: 20px;
}

.yield-icon.negative {
  color: #ef4444;
}

.yield-icon.neutral {
  color: var(--text-muted);
}

.yield-icon.low {
  color: #f59e0b;
}

.yield-icon.medium {
  color: var(--accent-orange);
}

.yield-icon.high {
  color: var(--accent-green);
}

.comparison-note {
  text-align: center;
  margin-top: 2rem;
}

.comparison-note p {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .section-title {
    font-size: 1.75rem;
    line-height: 1.2;
  }

  .section-subtitle {
    font-size: 0.95rem;
    line-height: 1.5;
  }

  .header-cell,
  .platform-cell,
  .yield-cell {
    padding: 0.875rem;
  }

  .header-cell {
    font-size: 0.95rem;
  }

  .platform-name {
    font-size: 0.9rem;
  }

  .yield-value {
    font-size: 0.95rem;
  }

  .table-row.highlight .yield-value {
    font-size: 1.05rem;
  }

  .table-row.highlight .platform-name {
    padding-left: 1.5rem;
  }

  .platform-logo {
    width: 20px;
    height: 20px;
  }

  .yield-icon {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .comparison-table {
    margin: 0 -1rem;
    border-radius: 0;
  }
  
  .header-cell,
  .platform-cell,
  .yield-cell {
    padding: 0.75rem 0.5rem;
  }
  
  .header-cell {
    font-size: 0.875rem;
  }
  
  .platform-name {
    font-size: 0.875rem;
  }
  
  .yield-value {
    font-size: 0.875rem;
  }
  
  .table-row.highlight .yield-value {
    font-size: 1rem;
  }
  
  .yield-icon {
    width: 16px;
    height: 16px;
  }
  
  .table-row.highlight::before {
    left: 0.5rem;
    font-size: 1rem;
  }
  
  .table-row.highlight .platform-name {
    padding-left: 1.25rem;
  }
}
