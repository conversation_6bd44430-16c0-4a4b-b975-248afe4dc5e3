.loading-spinner {
  display: inline-block;
  position: relative;
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
}

.loading-spinner.medium {
  width: 40px;
  height: 40px;
}

.loading-spinner.large {
  width: 60px;
  height: 60px;
}

.spinner-ring {
  display: inline-block;
  position: relative;
  width: 100%;
  height: 100%;
}

.spinner-ring div {
  box-sizing: border-box;
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spinner-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
}

.loading-spinner.primary .spinner-ring div {
  border-top-color: var(--accent-primary);
}

.loading-spinner.white .spinner-ring div {
  border-top-color: white;
}

.loading-spinner.gold .spinner-ring div {
  border-top-color: var(--accent-gold);
}

.spinner-ring div:nth-child(1) {
  animation-delay: -0.45s;
}

.spinner-ring div:nth-child(2) {
  animation-delay: -0.3s;
}

.spinner-ring div:nth-child(3) {
  animation-delay: -0.15s;
}

@keyframes spinner-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
