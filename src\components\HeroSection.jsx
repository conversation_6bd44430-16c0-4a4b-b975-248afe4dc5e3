import React from 'react';
import { useTranslation } from 'react-i18next';
import { Shield, Zap, Globe, Lock, TrendingUp, DollarSign } from 'lucide-react';
import AnimatedBackground from './AnimatedBackground';
import AuthCard from './AuthCard';
import './HeroSection.css';

const HeroSection = () => {
  const { t } = useTranslation();

  const handleGetStarted = () => {
    // Paketler bölümüne scroll et
    const plansSection = document.getElementById('plans');
    if (plansSection) {
      const headerHeight = 80; // Header yüksekliği
      const targetPosition = plansSection.offsetTop - headerHeight;

      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <section id="home" className="hero-section">
      <div className="hero-background">
        <div className="hero-pattern"></div>
        <div className="hero-gradient"></div>
        <AnimatedBackground variant="financial" />
      </div>

      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title text-shimmer fade-in-up">
              {t('hero.title')}
            </h1>
            <p className="hero-subtitle fade-in-up" dangerouslySetInnerHTML={{ __html: t('hero.subtitle') }}>
            </p>
            <div className="hero-features fade-in-up">
              <div className="feature-item">
                <Shield className="feature-icon" />
                <span>Güvenli & Lisanslı</span>
              </div>
              <div className="feature-item">
                <DollarSign className="feature-icon" />
                <span>Dolar Bazında</span>
              </div>
              <div className="feature-item">
                <TrendingUp className="feature-icon" />
                <span>Yüksek Getiri</span>
              </div>
              <div className="feature-item">
                <Zap className="feature-icon" />
                <span>Anında İşlem</span>
              </div>
              <div className="feature-item">
                <Globe className="feature-icon" />
                <span>Global Erişim</span>
              </div>
              <div className="feature-item">
                <Lock className="feature-icon" />
                <span>Blockchain Güvenliği</span>
              </div>
            </div>
            <div className="hero-cta fade-in-up">
              <button className="btn btn-primary btn-lg" onClick={handleGetStarted}>
                {t('hero.cta')}
                <TrendingUp size={20} />
              </button>
            </div>
          </div>

          <div className="hero-visual">
            <AuthCard />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
