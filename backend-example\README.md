# NovaYield Backend API

NovaYield yatırım platformu için Node.js + Express + MongoDB backend API'si.

## 🚀 Hızlı Başlangıç

### Gereksinimler
- Node.js 18+ 
- MongoDB (yerel veya MongoDB Atlas)
- Gmail hesabı (email gönder<PERSON><PERSON> i<PERSON>in)

### 1. <PERSON><PERSON>yi <PERSON>
```bash
# Backend klasörüne gidin
cd backend-example

# Bağımlılıkları yükleyin
npm install
```

### 2. Environment Dosyasını Ayarlayın
`.env` dosyasını düzenleyin:

```env
# Sunucu Ayarları
PORT=3001
NODE_ENV=development

# Veritabanı (MongoDB yerel)
MONGODB_URI=mongodb://localhost:27017/novayield

# JWT Secret (Güçlü bir şifre oluşturun)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE=24h

# Email Ayarları (Gmail)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Frontend URL
FRONTEND_URL=http://localhost:5173
```

### 3. MongoDB Kurulumu

#### Seçenek A: MongoDB Yerel Kurulum
1. [MongoDB Community Server](https://www.mongodb.com/try/download/community) indirin
2. Kurulumu tamamlayın
3. MongoDB servisini başlatın:
   ```bash
   # Windows
   net start MongoDB
   
   # macOS/Linux
   sudo systemctl start mongod
   ```

#### Seçenek B: MongoDB Atlas (Bulut)
1. [MongoDB Atlas](https://www.mongodb.com/atlas) hesabı oluşturun
2. Ücretsiz cluster oluşturun
3. Connection string'i alın
4. `.env` dosyasında `MONGODB_URI`'yi güncelleyin:
   ```env
   MONGODB_URI=mongodb+srv://username:<EMAIL>/novayield
   ```

### 4. Gmail App Password Oluşturun
1. Gmail hesabınızda 2-Factor Authentication'ı aktifleştirin
2. [App Passwords](https://myaccount.google.com/apppasswords) sayfasına gidin
3. "Mail" için yeni app password oluşturun
4. Oluşturulan şifreyi `.env` dosyasında `EMAIL_PASS` olarak kullanın

### 5. Sunucuyu Başlatın
```bash
# Development mode
npm run dev

# Production mode
npm start
```

Sunucu http://localhost:3001 adresinde çalışacak.

## 📁 Proje Yapısı

```
backend-example/
├── src/
│   ├── config/
│   │   └── database.js          # MongoDB bağlantısı
│   ├── controllers/
│   │   └── authController.js    # Authentication logic
│   ├── middleware/
│   │   ├── auth.js             # JWT middleware
│   │   └── validation.js       # Input validation
│   ├── models/
│   │   ├── User.js             # User model
│   │   └── Investment.js       # Investment model
│   ├── routes/
│   │   ├── auth.js             # Auth routes
│   │   ├── users.js            # User routes
│   │   └── investments.js      # Investment routes
│   └── utils/
│       └── email.js            # Email utilities
├── .env                        # Environment variables
├── server.js                   # Ana server dosyası
└── package.json               # Dependencies
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - Kullanıcı kaydı
- `POST /api/auth/login` - Kullanıcı girişi
- `GET /api/auth/verify/:token` - Email doğrulama
- `POST /api/auth/forgot-password` - Şifre sıfırlama isteği
- `POST /api/auth/reset-password/:token` - Şifre sıfırlama
- `GET /api/auth/verify-token` - Token doğrulama

### Users
- `GET /api/users/profile` - Profil bilgileri
- `PUT /api/users/profile` - Profil güncelleme
- `POST /api/users/change-password` - Şifre değiştirme

### Investments
- `POST /api/investments` - Yeni yatırım oluştur
- `GET /api/investments` - Kullanıcının yatırımları
- `GET /api/investments/:id` - Yatırım detayı
- `PUT /api/investments/:id/status` - Yatırım durumu güncelle

## 🔒 Güvenlik Özellikleri

- JWT Authentication
- Password hashing (bcrypt)
- Rate limiting
- Input validation
- CORS protection
- Helmet security headers
- Account lockout (5 failed attempts)

## 📧 Email Templates

Sistem otomatik olarak şu emailleri gönderir:
- Email doğrulama
- Şifre sıfırlama
- Hoş geldin mesajı

## 🧪 Test Etme

### 1. Health Check
```bash
curl http://localhost:3001/health
```

### 2. Kullanıcı Kaydı
```bash
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!",
    "firstName": "Test",
    "lastName": "User"
  }'
```

### 3. Kullanıcı Girişi
```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!"
  }'
```

## 🚀 Production Deployment

### Environment Variables
Production için şu değişkenleri güncelleyin:
```env
NODE_ENV=production
FRONTEND_URL=https://yourdomain.com
FRONTEND_PROD_URL=https://yourdomain.com
JWT_SECRET=very-strong-production-secret
```

### PM2 ile Deployment
```bash
# PM2 yükleyin
npm install -g pm2

# Uygulamayı başlatın
pm2 start server.js --name "novayield-api"

# Auto-restart ayarlayın
pm2 startup
pm2 save
```

## 🔧 Sorun Giderme

### MongoDB Bağlantı Hatası
- MongoDB servisinin çalıştığından emin olun
- Connection string'in doğru olduğunu kontrol edin
- Firewall ayarlarını kontrol edin

### Email Gönderme Hatası
- Gmail App Password'ün doğru olduğunu kontrol edin
- 2FA'nın aktif olduğunu kontrol edin
- SMTP ayarlarını kontrol edin

### Port Hatası
- 3001 portunun kullanımda olmadığını kontrol edin
- `.env` dosyasında farklı port belirleyin

## 📞 Destek

Herhangi bir sorun yaşarsanız:
1. Console loglarını kontrol edin
2. `.env` dosyasının doğru ayarlandığından emin olun
3. MongoDB ve Node.js versiyonlarını kontrol edin

## 📝 Lisans

MIT License - Detaylar için LICENSE dosyasına bakın.
